import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../api/api';

// Async thunks
export const updateLocation = createAsyncThunk(
  'location/updateLocation',
  async ({ latitude, longitude, heading, speed, accuracy }, { rejectWithValue }) => {
    try {
      const response = await api.put('/location/update', {
        latitude,
        longitude,
        heading,
        speed,
        accuracy
      });
      return { latitude, longitude, heading, speed, accuracy };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update location');
    }
  }
);

export const getServiceRequestLocations = createAsyncThunk(
  'location/getServiceRequestLocations',
  async (serviceRequestId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/location/service-request/${serviceRequestId}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get service request locations');
    }
  }
);

export const getNavigationRoute = createAsyncThunk(
  'location/getNavigationRoute',
  async (serviceRequestId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/navigation/${serviceRequestId}/route`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get navigation route');
    }
  }
);

const initialState = {
  currentLocation: null,
  serviceRequestLocations: {},
  navigationRoute: null,
  loading: false,
  error: null,
  locationPermission: null,
  isTracking: false,
};

const locationSlice = createSlice({
  name: 'location',
  initialState,
  reducers: {
    setCurrentLocation: (state, action) => {
      state.currentLocation = action.payload;
    },
    setLocationPermission: (state, action) => {
      state.locationPermission = action.payload;
    },
    setIsTracking: (state, action) => {
      state.isTracking = action.payload;
    },
    clearNavigationRoute: (state) => {
      state.navigationRoute = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Update location
      .addCase(updateLocation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateLocation.fulfilled, (state, action) => {
        state.loading = false;
        state.currentLocation = action.payload;
      })
      .addCase(updateLocation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Get service request locations
      .addCase(getServiceRequestLocations.fulfilled, (state, action) => {
        state.serviceRequestLocations = action.payload;
      })
      // Get navigation route
      .addCase(getNavigationRoute.fulfilled, (state, action) => {
        state.navigationRoute = action.payload;
      });
  },
});

export const {
  setCurrentLocation,
  setLocationPermission,
  setIsTracking,
  clearNavigationRoute,
  clearError,
} = locationSlice.actions;

export default locationSlice.reducer;
