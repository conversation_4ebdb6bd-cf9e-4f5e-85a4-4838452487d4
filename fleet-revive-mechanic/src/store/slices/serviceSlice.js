import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../api/api';

// Async thunks
export const fetchServiceRequests = createAsyncThunk(
  'service/fetchServiceRequests',
  async (_, { rejectWithValue }) => {
    try {
      console.log('🔍 Mechanic App: Fetching service requests...');
      const response = await api.get('/requests');
      console.log('✅ Mechanic App: Service requests fetched successfully:', response.data.length, 'requests');
      console.log('📋 Mechanic App: Latest request:', response.data[response.data.length - 1]);
      console.log('📋 Mechanic App: First request:', response.data[0]);
      return response.data;
    } catch (error) {
      console.error('❌ Mechanic App: Failed to fetch service requests:', error.response?.data || error.message);
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch service requests');
    }
  }
);

export const fetchServiceRequestDetails = createAsyncThunk(
  'service/fetchServiceRequestDetails',
  async (requestId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/requests/${requestId}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch service request details');
    }
  }
);

export const acceptAssignment = createAsyncThunk(
  'service/acceptAssignment',
  async (assignmentId, { rejectWithValue }) => {
    try {
      const response = await api.put(`/assignments/${assignmentId}/accept`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to accept assignment');
    }
  }
);

export const rejectAssignment = createAsyncThunk(
  'service/rejectAssignment',
  async (assignmentId, { rejectWithValue }) => {
    try {
      const response = await api.put(`/assignments/${assignmentId}/reject`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to reject assignment');
    }
  }
);

export const acceptServiceRequest = createAsyncThunk(
  'service/acceptServiceRequest',
  async ({ serviceRequestId, estimatedArrival }, { rejectWithValue }) => {
    try {
      const response = await api.post('/assignments/accept', {
        service_request_id: serviceRequestId,
        estimated_arrival: estimatedArrival,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to accept service request');
    }
  }
);

export const rejectServiceRequest = createAsyncThunk(
  'service/rejectServiceRequest',
  async ({ serviceRequestId, rejectionReason }, { rejectWithValue }) => {
    try {
      const response = await api.post('/assignments/reject', {
        service_request_id: serviceRequestId,
        rejection_reason: rejectionReason,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to reject service request');
    }
  }
);

export const startWork = createAsyncThunk(
  'service/startWork',
  async (assignmentId, { rejectWithValue }) => {
    try {
      console.log('🔧 MECHANIC: Starting work for assignment:', assignmentId);

      if (!assignmentId) {
        console.error('❌ MECHANIC: No assignment ID provided for startWork');
        return rejectWithValue('Assignment ID is required to start work');
      }

      const response = await api.put(`/assignments/${assignmentId}/start-work`);
      console.log('✅ MECHANIC: Work started successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ MECHANIC: Error starting work:', error);
      console.error('❌ MECHANIC: Error response:', error.response?.data);
      console.error('❌ MECHANIC: Error status:', error.response?.status);

      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          'Failed to start work - please try again';

      console.error('❌ MECHANIC: Final error message:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const updateServiceStatus = createAsyncThunk(
  'service/updateServiceStatus',
  async ({ requestId, status }, { rejectWithValue }) => {
    try {
      const response = await api.put(`/requests/${requestId}/status`, { status });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update service status');
    }
  }
);

export const completeAssignment = createAsyncThunk(
  'service/completeAssignment',
  async ({ assignmentId, notes }, { rejectWithValue }) => {
    try {
      console.log('🏁 MECHANIC: Completing assignment:', assignmentId);
      console.log('🏁 MECHANIC: Completion notes:', notes);

      if (!assignmentId) {
        console.error('❌ MECHANIC: No assignment ID provided for completeAssignment');
        return rejectWithValue('Assignment ID is required to complete work');
      }

      const response = await api.put(`/assignments/${assignmentId}/complete`, {
        completion_notes: notes
      });
      console.log('✅ MECHANIC: Assignment completed successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ MECHANIC: Error completing assignment:', error);
      console.error('❌ MECHANIC: Error response:', error.response?.data);
      console.error('❌ MECHANIC: Error status:', error.response?.status);

      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          'Failed to complete assignment - please try again';

      console.error('❌ MECHANIC: Final error message:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const shareETA = createAsyncThunk(
  'service/shareETA',
  async ({ serviceRequestId, eta }, { rejectWithValue }) => {
    try {
      const response = await api.post('/location/share-eta', {
        service_request_id: serviceRequestId,
        eta
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to share ETA');
    }
  }
);

const initialState = {
  requests: [],
  currentRequest: null,
  assignments: [],
  loading: false,
  error: null,
  refreshing: false,
  // Service notification dialog state
  notificationDialog: {
    visible: false,
    serviceRequest: null,
  },
};

const serviceSlice = createSlice({
  name: 'service',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentRequest: (state, action) => {
      state.currentRequest = action.payload;
    },
    updateRequestStatus: (state, action) => {
      const { requestId, status } = action.payload;
      const request = state.requests.find(r => r.id === requestId);
      if (request) {
        request.status = status;
      }
      if (state.currentRequest && state.currentRequest.id === requestId) {
        state.currentRequest.status = status;
      }
    },
    addNewRequest: (state, action) => {
      state.requests.unshift(action.payload);
    },
    removeRequest: (state, action) => {
      state.requests = state.requests.filter(r => r.id !== action.payload);
    },
    // Service notification dialog actions
    showNotificationDialog: (state, action) => {
      state.notificationDialog.visible = true;
      state.notificationDialog.serviceRequest = action.payload;
    },
    hideNotificationDialog: (state) => {
      state.notificationDialog.visible = false;
      state.notificationDialog.serviceRequest = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch service requests
      .addCase(fetchServiceRequests.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchServiceRequests.fulfilled, (state, action) => {
        state.loading = false;
        state.requests = action.payload;
        state.refreshing = false;
      })
      .addCase(fetchServiceRequests.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.refreshing = false;
      })
      // Fetch service request details
      .addCase(fetchServiceRequestDetails.fulfilled, (state, action) => {
        state.currentRequest = action.payload;
      })
      // Accept assignment
      .addCase(acceptAssignment.fulfilled, (state, action) => {
        // Update the assignment status
        const assignment = state.assignments.find(a => a.id === action.payload.id);
        if (assignment) {
          assignment.status = 'accepted';
        }
      })

      // Start work
      .addCase(startWork.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(startWork.fulfilled, (state, action) => {
        state.loading = false;
        console.log('✅ Redux: startWork fulfilled with:', action.payload);

        // Update the assignment status to in_progress
        const assignment = state.assignments.find(a => a.id === action.payload.assignment?.id);
        if (assignment) {
          assignment.status = 'in_progress';
          assignment.work_started_at = action.payload.assignment?.work_started_at;
          assignment.actual_arrival = action.payload.assignment?.actual_arrival;
        }

        // Update current request status if it matches
        if (state.currentRequest && action.payload.assignment) {
          const requestAssignment = state.currentRequest.assignments?.find(a => a.id === action.payload.assignment.id);
          if (requestAssignment) {
            requestAssignment.status = 'in_progress';
            requestAssignment.work_started_at = action.payload.assignment.work_started_at;
            requestAssignment.actual_arrival = action.payload.assignment.actual_arrival;
          }
        }
      })
      .addCase(startWork.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        console.error('❌ Redux: startWork rejected with:', action.payload);
      })

      // Complete assignment
      .addCase(completeAssignment.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(completeAssignment.fulfilled, (state, action) => {
        state.loading = false;
        console.log('✅ Redux: completeAssignment fulfilled with:', action.payload);

        // Update the assignment status
        const assignment = state.assignments.find(a => a.id === action.payload.assignment?.id);
        if (assignment) {
          assignment.status = 'completed';
          assignment.completed_at = action.payload.assignment?.completed_at;
        }
        // Update current request status if it matches
        if (state.currentRequest && action.payload.assignment) {
          state.currentRequest.status = 'completed';
          const requestAssignment = state.currentRequest.assignments?.find(a => a.id === action.payload.assignment.id);
          if (requestAssignment) {
            requestAssignment.status = 'completed';
            requestAssignment.completed_at = action.payload.assignment.completed_at;
          }
        }
      })
      .addCase(completeAssignment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        console.error('❌ Redux: completeAssignment rejected with:', action.payload);
      });
  },
});

export const {
  clearError,
  setCurrentRequest,
  updateRequestStatus,
  addNewRequest,
  removeRequest,
  showNotificationDialog,
  hideNotificationDialog
} = serviceSlice.actions;

export default serviceSlice.reducer;
