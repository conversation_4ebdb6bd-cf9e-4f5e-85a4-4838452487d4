import React, { useState, useEffect, useRef } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
  Dimensions,
} from "react-native";
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Circle } from "react-native-maps";
import { Ionicons } from "@expo/vector-icons";
import * as Location from "expo-location";
import { useTheme } from "../context/ThemeContext";
import socketService from "../services/socketService";
import directionsService from "../services/directionsService";
import { ROUTE_CONFIG } from "../config/constants";

const { width, height } = Dimensions.get("window");

const RouteTrackingMap = ({
  serviceRequest,
  mechanicLocation,
  driverLocation,
  onLocationUpdate,
  style,
}) => {
  const { theme } = useTheme();
  const mapRef = useRef(null);

  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [routeSteps, setRouteSteps] = useState([]);
  const [estimatedTime, setEstimatedTime] = useState(null);
  const [distance, setDistance] = useState(null);
  const [isTracking, setIsTracking] = useState(false);
  const [mapType, setMapType] = useState("standard");
  const [showTraffic, setShowTraffic] = useState(true);
  const [isLoadingRoute, setIsLoadingRoute] = useState(false);

  const styles = createStyles(theme);

  useEffect(() => {
    if (mechanicLocation && driverLocation) {
      calculateRoute();
      fitMapToMarkers();
    }
  }, [mechanicLocation, driverLocation]);

  useEffect(() => {
    // Start location tracking when component mounts
    startLocationTracking();

    // Listen for driver location updates via socket
    socketService.addEventListener(
      "location_update",
      handleDriverLocationUpdate
    );

    return () => {
      stopLocationTracking();
      socketService.removeEventListener("location_update", handleDriverLocationUpdate);
    };
  }, []);

  // Start real-time location tracking
  const startLocationTracking = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          "Permission Denied",
          "Location permission is required for tracking"
        );
        return;
      }

      setIsTracking(true);

      // Watch position with high accuracy
      const watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000, // Update every 5 seconds
          distanceInterval: 10, // Update every 10 meters
        },
        (location) => {
          const newLocation = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            heading: location.coords.heading,
            speed: location.coords.speed,
            accuracy: location.coords.accuracy,
          };

          // Update local state
          if (onLocationUpdate) {
            onLocationUpdate(newLocation);
          }

          // Send to backend via socket
          socketService.updateMechanicLocation(newLocation);
        }
      );

      // Store watch ID for cleanup
      setIsTracking(watchId);
    } catch (error) {
      console.error("Error starting location tracking:", error);
      Alert.alert("Error", "Failed to start location tracking");
    }
  };

  // Stop location tracking
  const stopLocationTracking = () => {
    if (isTracking && typeof isTracking === "object") {
      isTracking.remove();
      setIsTracking(false);
    }
  };

  // Handle driver location updates from socket
  const handleDriverLocationUpdate = (data) => {
    if (data.serviceRequestId === serviceRequest?.id && data.userType === "driver") {
      const newDriverLocation = {
        latitude: data.latitude,
        longitude: data.longitude,
        heading: data.heading,
        speed: data.speed,
      };

      console.log("📍 RouteTrackingMap: Driver location updated:", newDriverLocation);

      // Update route if driver moved significantly
      if (mechanicLocation) {
        calculateRoute(mechanicLocation, newDriverLocation);
      }
    }
  };

  // Calculate route between mechanic and driver using Google Directions API
  const calculateRoute = async (
    mechanic = mechanicLocation,
    driver = driverLocation
  ) => {
    if (!mechanic || !driver) return;

    setIsLoadingRoute(true);
    try {
      const origin = {
        latitude: mechanic.latitude,
        longitude: mechanic.longitude,
      };
      const destination = {
        latitude: driver.latitude,
        longitude: driver.longitude,
      };

      // Get detailed route with turn-by-turn directions
      const routeData = await directionsService.getRoute(origin, destination, {
        ...ROUTE_CONFIG,
        avoidTolls: false, // Allow tolls for faster routes
        alternatives: false, // Get single best route
      });

      // Set route coordinates from Google Directions API
      setRouteCoordinates(routeData.coordinates);
      setRouteSteps(routeData.steps);

      // Set distance and time from API response
      const distanceKm = routeData.summary.distance.value / 1000;
      const durationMinutes = Math.round(routeData.summary.duration.value / 60);

      setDistance(distanceKm);
      setEstimatedTime(durationMinutes);

      console.log(`📍 Route calculated: ${distanceKm.toFixed(1)}km, ${durationMinutes}min`);
      console.log(`🛣️ Route has ${routeData.steps.length} turn-by-turn steps`);

    } catch (error) {
      console.error("Error calculating route:", error);

      // Fallback to straight line if API fails
      const coordinates = [
        { latitude: mechanic.latitude, longitude: mechanic.longitude },
        { latitude: driver.latitude, longitude: driver.longitude },
      ];
      setRouteCoordinates(coordinates);

      // Calculate estimated time and distance using fallback method
      const dist = calculateDistance(mechanic, driver);
      setDistance(dist);
      const estimatedMinutes = Math.round((dist / 40) * 60);
      setEstimatedTime(estimatedMinutes);

      console.log("📍 Using fallback straight-line route");
    } finally {
      setIsLoadingRoute(false);
    }
  };

  // Calculate distance between two points using Haversine formula
  const calculateDistance = (point1, point2) => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = ((point2.latitude - point1.latitude) * Math.PI) / 180;
    const dLon = ((point2.longitude - point1.longitude) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((point1.latitude * Math.PI) / 180) *
        Math.cos((point2.latitude * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  // Fit map to show both markers
  const fitMapToMarkers = () => {
    if (!mechanicLocation || !driverLocation || !mapRef.current) return;

    const coordinates = [
      {
        latitude: mechanicLocation.latitude,
        longitude: mechanicLocation.longitude,
      },
      {
        latitude: driverLocation.latitude,
        longitude: driverLocation.longitude,
      },
    ];

    mapRef.current.fitToCoordinates(coordinates, {
      edgePadding: { top: 100, right: 100, bottom: 100, left: 100 },
      animated: true,
    });
  };

  // Toggle map type
  const toggleMapType = () => {
    const types = ["standard", "satellite", "hybrid"];
    const currentIndex = types.indexOf(mapType);
    const nextIndex = (currentIndex + 1) % types.length;
    setMapType(types[nextIndex]);
  };

  // Center map on mechanic location
  const centerOnMechanic = () => {
    if (!mechanicLocation || !mapRef.current) return;

    mapRef.current.animateToRegion(
      {
        latitude: mechanicLocation.latitude,
        longitude: mechanicLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      },
      1000
    );
  };

  // Center map on driver location
  const centerOnDriver = () => {
    if (!driverLocation || !mapRef.current) return;

    mapRef.current.animateToRegion(
      {
        latitude: driverLocation.latitude,
        longitude: driverLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      },
      1000
    );
  };

  if (!mechanicLocation && !driverLocation) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.loadingContainer}>
          <Ionicons
            name="location-outline"
            size={48}
            color={theme.colors.textSecondary}
          />
          <Text style={styles.loadingText}>Waiting for location data...</Text>
        </View>
      </View>
    );
  }
  return (
    <View style={[styles.container, style]}>
      <MapView
        ref={mapRef}
        style={styles.map}
        mapType={mapType}
        showsUserLocation={false}
        showsMyLocationButton={false}
        showsCompass={true}
        showsTraffic={showTraffic}
        showsBuildings={true}
        showsIndoors={true}
        showsPointsOfInterest={true}
        customMapStyle={theme.mapStyle}
        initialRegion={{
          latitude: mechanicLocation?.latitude,
          longitude: mechanicLocation?.longitude,
          latitudeDelta: 0.02,
          longitudeDelta: 0.02,
        }}
      >
        {/* Mechanic location marker */}
        <Marker
          coordinate={{
            latitude: mechanicLocation.latitude,
            longitude: mechanicLocation.longitude,
          }}
          title="Your Location"
          description="Mechanic"
          pinColor="#4CAF50"
        >
          <View style={[styles.mechanicMarker, { backgroundColor: "#4CAF50" }]}>
            <Ionicons name="build" size={20} color="white" />
          </View>
        </Marker>

        {/* Driver location marker */}
        {driverLocation && (
          <Marker
            coordinate={{
              latitude: driverLocation.latitude,
              longitude: driverLocation.longitude,
            }}
            title="Driver Location"
            description={serviceRequest?.driver_name || "Driver"}
            pinColor="#FF5722"
          >
            <View style={[styles.driverMarker, { backgroundColor: "#FF5722" }]}>
              <Ionicons name="car" size={20} color="white" />
            </View>
          </Marker>
        )}

        {/* Route polyline */}
        {routeCoordinates.length > 0 && (
          <Polyline
            coordinates={routeCoordinates}
            strokeColor={theme.colors.primary}
            strokeWidth={4}
            lineDashPattern={[5, 5]}
          />
        )}

        {/* Accuracy circles */}
        <Circle
          center={{
            latitude: mechanicLocation.latitude,
            longitude: mechanicLocation.longitude,
          }}
          radius={mechanicLocation.accuracy || 50}
          fillColor="rgba(76, 175, 80, 0.1)"
          strokeColor="rgba(76, 175, 80, 0.3)"
          strokeWidth={1}
        />
      </MapView>

      {/* Map Controls */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity style={styles.controlButton} onPress={toggleMapType}>
          <Ionicons
            name="layers-outline"
            size={24}
            color={theme.colors.primary}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => setShowTraffic(!showTraffic)}
        >
          <Ionicons
            name={showTraffic ? "car" : "car-outline"}
            size={24}
            color={
              showTraffic ? theme.colors.primary : theme.colors.textSecondary
            }
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={centerOnMechanic}
        >
          <Ionicons name="locate" size={24} color={theme.colors.primary} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.controlButton} onPress={centerOnDriver}>
          <Ionicons name="navigate" size={24} color={theme.colors.primary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={fitMapToMarkers}
        >
          <Ionicons name="resize" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Route Information */}
      {estimatedTime && distance && (
        <View style={styles.routeInfoContainer}>
          <View style={styles.routeInfo}>
            <Ionicons
              name="time-outline"
              size={16}
              color={theme.colors.primary}
            />
            <Text style={styles.routeInfoText}>
              {estimatedTime} min • {distance.toFixed(1)} km
            </Text>
          </View>
          <View style={styles.trackingStatus}>
            <View
              style={[
                styles.trackingDot,
                { backgroundColor: isTracking ? "#4CAF50" : "#FF5722" },
              ]}
            />
            <Text style={styles.trackingText}>
              {isTracking ? "Tracking Active" : "Tracking Inactive"}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

const createStyles = (theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    map: {
      flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: theme.colors.background,
    },
    loadingText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      marginTop: 16,
    },
    mechanicMarker: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 3,
      borderColor: "white",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 5,
    },
    driverMarker: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 3,
      borderColor: "white",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 5,
    },
    controlsContainer: {
      position: "absolute",
      top: 50,
      right: 16,
      gap: 8,
    },
    controlButton: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: theme.colors.surface,
      justifyContent: "center",
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    routeInfoContainer: {
      position: "absolute",
      bottom: 20,
      left: 16,
      right: 16,
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 5,
    },
    routeInfo: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
    },
    routeInfoText: {
      fontSize: 16,
      fontWeight: "600",
      color: theme.colors.textPrimary,
      marginLeft: 8,
    },
    trackingStatus: {
      flexDirection: "row",
      alignItems: "center",
    },
    trackingDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginRight: 8,
    },
    trackingText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
  });

export default RouteTrackingMap;
