import { io } from "socket.io-client";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";
import serviceNotificationManager from "./serviceNotificationManager";
import { store } from "../store/store";

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000;
    this.listeners = new Map();
  }

  // Get Socket.IO server URL
  getServerUrl() {
    const baseUrl =
      // __DEV__
      //   ? Platform.select({
      //       ios: "http://************:5001",
      //       android: "http://************:5001",
      //       web: "http://localhost:5001",
      //     })
      //   :
      "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com";

    return baseUrl;
  }

  // Initialize and connect to Socket.IO server
  async connect() {
    try {
      if (this.socket && this.isConnected) {
        console.log("🔌 Socket already connected");
        return;
      }

      const token = await AsyncStorage.getItem("token");
      if (!token) {
        console.log("❌ No auth token found, cannot connect to socket");
        return;
      }

      console.log("🔑 Mechanic Socket - Token found:", token ? "Yes" : "No");
      console.log(
        "🔑 Mechanic Socket - Token preview:",
        token ? token.substring(0, 50) + "..." : "No token"
      );
      console.log(
        "🔑 Mechanic Socket - Token length:",
        token ? token.length : 0
      );

      const serverUrl = this.getServerUrl();
      console.log("🔌 Connecting to Socket.IO server:", serverUrl);

      this.socket = io(serverUrl, {
        auth: {
          token: token,
        },
        transports: ["polling", "websocket"], // Start with polling for stability
        timeout: 30000, // Increased timeout for mobile networks
        reconnection: true,
        reconnectionAttempts: 10, // More attempts
        reconnectionDelay: 2000, // Start with 2 seconds
        reconnectionDelayMax: 15000, // Max 15 seconds between attempts
        randomizationFactor: 0.3, // Less randomization
        forceNew: true, // Force new connection to avoid conflicts
        upgrade: true, // Allow transport upgrades
        rememberUpgrade: false, // Don't remember upgrades for stability
        // Ping/Pong configuration for mobile networks
        pingTimeout: 60000, // 60 seconds before considering connection dead
        pingInterval: 25000, // Send ping every 25 seconds
        // Additional mobile-friendly options
        autoConnect: true,
        closeOnBeforeunload: false,
      });

      this.setupEventListeners();
    } catch (error) {
      console.error("❌ Error connecting to socket:", error);
    }
  }

  // Setup Socket.IO event listeners
  setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on("connect", () => {
      console.log("✅ Socket connected:", this.socket.id);
      this.isConnected = true;
      this.reconnectAttempts = 0;

      // Clear any existing connection monitoring
      if (this.connectionMonitor) {
        clearInterval(this.connectionMonitor);
      }

      // Start connection health monitoring
      this.startConnectionMonitoring();

      this.joinMechanicRoom();
    });

    this.socket.on("disconnect", (reason) => {
      console.log("❌ Socket disconnected:", reason);
      this.isConnected = false;

      // Clear connection monitoring
      if (this.connectionMonitor) {
        clearInterval(this.connectionMonitor);
        this.connectionMonitor = null;
      }

      this.notifyListeners("disconnected", { reason });

      // Handle different disconnect reasons
      if (reason === "ping timeout") {
        console.log("🔄 Ping timeout detected, attempting immediate reconnect...");
        // For ping timeouts, try to reconnect immediately
        setTimeout(() => {
          if (!this.isConnected && this.socket) {
            this.socket.connect();
          }
        }, 1000);
      } else if (reason !== "io client disconnect") {
        // For other reasons, use normal reconnect logic
        this.scheduleReconnect();
      }
    });

    this.socket.on("connect_error", (error) => {
      console.error("❌ Mechanic Socket connection error:", error);
      console.error("❌ Error details:", {
        message: error.message,
        description: error.description,
        context: error.context,
        type: error.type,
      });
      this.isConnected = false;
      this.reconnectAttempts++;

      // Add specific handling for authentication errors
      if (error.message === "Authentication error") {
        console.error("🔐 Authentication failed - check token validity");
        console.error(
          "💡 This usually means the JWT secret changed or token was signed with different secret"
        );
        console.error("🔄 Please logout and login again to get a fresh token");

        // Stop reconnection attempts for auth errors
        this.reconnectAttempts = this.maxReconnectAttempts;
        return;
      }

      // Handle network errors
      if (
        error.message.includes("timeout") ||
        error.message.includes("network")
      ) {
        console.error("🌐 Network connectivity issue - will retry");
      }

      // Schedule reconnection if we haven't exceeded max attempts
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        console.log(
          `🔄 Will attempt reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay}ms`
        );
      } else {
        console.error(
          "❌ Max reconnection attempts reached. Socket connection failed."
        );
      }
    });

    // Service request events
    this.socket.on("new_service_request", (data) => {
      console.log("🔔 New service request received:", data);
      this.handleNewServiceRequest(data);
    });

    this.socket.on("service_request_cancelled", (data) => {
      console.log("❌ Service request cancelled:", data);
      this.handleServiceRequestCancelled(data);
    });

    this.socket.on("service_request_unavailable", (data) => {
      console.log("🚫 Service request no longer available:", data);
      this.handleServiceRequestUnavailable(data);
    });

    this.socket.on("assignment_updated", (data) => {
      console.log("🔄 Assignment updated:", data);
      this.handleAssignmentUpdated(data);
    });

    this.socket.on("assignment_confirmed", (data) => {
      console.log("✅ Assignment confirmed:", data);
      this.handleAssignmentConfirmed(data);
    });

    // Chat events
    this.socket.on("new_message", (data) => {
      console.log("💬 New message received:", data);
      this.handleNewMessage(data);
    });

    // Location events
    this.socket.on("location_update", (data) => {
      console.log("📍 Location update received:", data);
      this.handleLocationUpdate(data);
    });

    // Connection health monitoring
    this.socket.on("pong", (data) => {
      // Server responded to our ping - connection is healthy
      const latency = Date.now() - data.clientTimestamp;
      console.log(`🏓 Pong received - Connection latency: ${latency}ms`);
    });

    // Custom event listeners
    this.listeners.forEach((callback, event) => {
      this.socket.on(event, callback);
    });
  }

  // Join mechanic-specific room for targeted notifications
  joinMechanicRoom() {
    const state = store.getState();
    const mechanicId = state.auth.user?.id;

    if (mechanicId) {
      this.socket.emit("join_mechanic_room", { mechanicId });
      console.log("🏠 Joined mechanic room:", mechanicId);
    }
  }

  // Handle new service request notification
  handleNewServiceRequest(data) {
    console.log("🚛 New service request received via Socket.IO:", data);

    const { serviceRequest, distance, urgency } = data;

    // Validate service request data
    if (!serviceRequest) {
      console.error("❌ No service request data in Socket.IO message:", data);
      return;
    }

    if (!serviceRequest.id) {
      console.error(
        "❌ Service request missing ID in Socket.IO data:",
        serviceRequest
      );
      return;
    }

    // Enhance service request with additional data
    const enhancedRequest = {
      ...serviceRequest,
      distance: distance,
      urgency: urgency || serviceRequest.urgency || "normal",
      receivedAt: new Date().toISOString(),
    };

    console.log("🔔 Enhanced service request from Socket.IO:", enhancedRequest);

    // Pass to notification manager
    try {
      serviceNotificationManager.handleNewServiceRequest(enhancedRequest);
      console.log(
        "✅ Service request passed to notification manager via Socket.IO"
      );
    } catch (error) {
      console.error("❌ Error handling Socket.IO service request:", error);
    }
  }

  // Handle service request cancellation
  handleServiceRequestCancelled(data) {
    const { serviceRequestId, reason } = data;

    // Remove from pending requests if exists
    serviceNotificationManager.clearRequestTimeout(serviceRequestId);

    // Notify user
    console.log(
      `🚫 Service request ${serviceRequestId} was cancelled: ${reason}`
    );
  }

  // Handle service request becoming unavailable (accepted by another mechanic)
  handleServiceRequestUnavailable(data) {
    const { service_request_id, reason, accepted_by } = data;

    // Remove from pending requests
    serviceNotificationManager.clearRequestTimeout(service_request_id);

    // Notify listeners
    this.notifyListeners("service_request_unavailable", data);

    console.log(
      `🚫 Service request ${service_request_id} no longer available: ${reason} by ${accepted_by}`
    );
  }

  // Handle assignment confirmation (when this mechanic accepts a request)
  handleAssignmentConfirmed(data) {
    const { assignment, service_request } = data;

    // Notify listeners
    this.notifyListeners("assignment_confirmed", data);

    console.log(
      `✅ Assignment confirmed for service request ${service_request.id}`
    );
  }

  // Handle assignment updates
  handleAssignmentUpdated(data) {
    const { assignmentId, status, serviceRequestId } = data;

    console.log(`🔄 Assignment ${assignmentId} status updated to: ${status}`);

    // Update local state or notify components
    this.notifyListeners("assignment_updated", data);
  }

  // Handle new chat messages
  handleNewMessage(data) {
    const { serviceRequestId, message, senderType, senderId } = data;

    // Only handle messages from drivers (not our own messages)
    if (senderType === "driver") {
      console.log(`💬 New message from driver in service ${serviceRequestId}`);

      // Notify chat components
      this.notifyListeners("new_message", data);

      // Show local notification if app is in background
      this.showChatNotification(data);
    }
  }

  // Handle location updates
  handleLocationUpdate(data) {
    const { userId, userType, latitude, longitude, serviceRequestId } = data;

    if (userType === "driver") {
      console.log(`📍 Driver location updated for service ${serviceRequestId}`);

      // Notify map components
      this.notifyListeners("location_update", data);
    }
  }

  // Show chat notification
  async showChatNotification(data) {
    try {
      const { message, senderName } = data;

      // This would integrate with your notification service
      console.log(`💬 Chat notification: ${senderName}: ${message}`);
    } catch (error) {
      console.error("Error showing chat notification:", error);
    }
  }

  // Send mechanic location update
  updateMechanicLocation(location, serviceRequestId = null) {
    if (!this.isConnected || !this.socket) return;

    const locationData = {
      latitude: location.latitude,
      longitude: location.longitude,
      heading: location.heading,
      speed: location.speed,
      accuracy: location.accuracy,
      timestamp: new Date().toISOString(),
      userType: "mechanic", // CRITICAL: Add user type
    };

    // Add service request ID if provided
    if (serviceRequestId) {
      locationData.serviceRequestId = serviceRequestId;
    }

    console.log("📍 Mechanic: Sending location update:", locationData);
    this.socket.emit("location_update", locationData);
  }

  // Send chat message
  sendChatMessage(serviceRequestId, message, messageType = "text") {
    if (!this.isConnected || !this.socket) return;

    this.socket.emit("send_message", {
      serviceRequestId,
      message,
      messageType,
      timestamp: new Date().toISOString(),
    });
  }

  // Join service request room for real-time updates
  joinServiceRoom(serviceRequestId) {
    if (!this.isConnected || !this.socket) return;

    this.socket.emit("join_service_request", serviceRequestId);
    console.log("🏠 Joined service room:", serviceRequestId);
  }

  // Leave service request room
  leaveServiceRoom(serviceRequestId) {
    if (!this.isConnected || !this.socket) return;

    this.socket.emit("leave_service_request", serviceRequestId);
    console.log("🚪 Left service room:", serviceRequestId);
  }

  // Update mechanic status
  updateMechanicStatus(status) {
    if (!this.isConnected || !this.socket) return;

    this.socket.emit("mechanic_status_update", {
      status,
      timestamp: new Date().toISOString(),
    });
  }

  // Notify that work has started
  notifyWorkStarted(serviceRequestId) {
    if (!this.isConnected || !this.socket) return;

    this.socket.emit("work_started", {
      serviceRequestId,
      timestamp: new Date().toISOString(),
    });
    console.log(
      "🔧 Notified driver that work has started for service:",
      serviceRequestId
    );
  }

  // Notify that service has been completed
  notifyServiceCompleted(serviceRequestId) {
    if (!this.isConnected || !this.socket) return;

    this.socket.emit("service_completed", {
      serviceRequestId,
      timestamp: new Date().toISOString(),
    });
    console.log(
      "✅ Notified driver that service has been completed for service:",
      serviceRequestId
    );
  }

  // Notify that service has been cancelled
  notifyServiceCancelled(serviceRequestId, reason = "Service cancelled by mechanic") {
    if (!this.isConnected || !this.socket) return;

    this.socket.emit("service_cancelled", {
      serviceRequestId,
      reason,
      timestamp: new Date().toISOString(),
    });
    console.log(
      "🚫 Notified driver that service has been cancelled for service:",
      serviceRequestId
    );
  }

  // Add custom event listener
  addEventListener(event, callback) {
    this.listeners.set(event, callback);

    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  // Remove custom event listener
  removeEventListener(event) {
    const callback = this.listeners.get(event);

    if (callback && this.socket) {
      this.socket.off(event, callback);
    }

    this.listeners.delete(event);
  }

  // Notify custom listeners
  notifyListeners(event, data) {
    const callback = this.listeners.get(event);
    if (callback) {
      callback(data);
    }
  }

  // Disconnect from socket
  disconnect() {
    if (this.socket) {
      console.log("🔌 Disconnecting from socket");
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Manual reconnection method
  async reconnect() {
    console.log("🔄 Manual reconnection requested...");

    // Disconnect existing connection
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    // Reset connection state
    this.isConnected = false;
    this.reconnectAttempts = 0;

    // Attempt to reconnect
    await this.connect();
  }

  // Start connection health monitoring
  startConnectionMonitoring() {
    // Monitor connection health every 30 seconds
    this.connectionMonitor = setInterval(() => {
      if (this.socket && this.isConnected) {
        // Send a ping to check if connection is alive
        this.socket.emit('ping', { timestamp: Date.now() });
      }
    }, 30000);
  }

  // Schedule reconnection attempt
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log("❌ Max reconnection attempts reached");
      return;
    }

    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts),
      10000
    );

    console.log(
      `🔄 Scheduling reconnect attempt ${this.reconnectAttempts + 1}/${
        this.maxReconnectAttempts
      } in ${delay}ms`
    );

    setTimeout(() => {
      if (!this.isConnected) {
        this.reconnectAttempts++;
        this.connect();
      }
    }, delay);
  }

  // Get connection status
  getConnectionStatus() {
    return {
      connected: this.isConnected,
      socketId: this.socket?.id || null,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
    };
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
