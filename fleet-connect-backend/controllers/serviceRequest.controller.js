// File: controllers/serviceRequest.controller.js
import db from "../models/index.js";
import pushNotificationService from "../services/pushNotification.service.js";
const { ServiceRequest, Mechanic, Driver, TruckRepairCompany, ServiceAssignment } = db;

const serviceRequestController = {
  // POST /api/requests
  create: async (req, res) => {
    try {
      // Handle both direct API calls and internal calls from driver controller
      let requestData;
      let driver_id;
      let isInternalCall = false;

      if (req && req.body && req.user) {
        // Direct API call (has req.body and req.user)
        requestData = req.body;
        driver_id = req.user.id;
        isInternalCall = false;
      } else if (req && !req.body) {
        // Internal call from driver controller (req is actually the data object)
        requestData = req;
        driver_id = req.driver_id;
        isInternalCall = true;
      } else {
        throw new Error('Invalid request format');
      }

      const {
        issue_type,
        truck_info,
        issue_description,
        latitude,
        longitude,
        status = 'pending',
        transport_company_id,
      } = requestData;

      // Create the service request
      const serviceRequest = await ServiceRequest.create({
        issue_type,
        truck_info,
        issue_description,
        latitude,
        longitude,
        status,
        driver_id,
        transport_company_id,
      });

      // Get driver information for the notification
      const driver = await Driver.findByPk(driver_id);

      console.log('🚛 New service request created:', serviceRequest.id);

      // Broadcast to all available mechanics via Socket.IO
      console.log('📡 Broadcasting service request to mechanics...');

      // Create notification data
      const notificationData = {
        serviceRequest: {
          ...serviceRequest.toJSON(),
          driver_name: driver ? driver.name : 'Unknown Driver',
        },
        type: 'new_service_request',
        urgency: issue_type === 'brake' || issue_type === 'engine' ? 'high' : 'normal',
        timestamp: new Date().toISOString(),
      };

      // Try to get io instance (works for both API calls and internal calls)
      let io = null;

      // First try to get from request (for API calls)
      if (!isInternalCall && req && req.app) {
        io = req.app.get('io');
      }

      // If not available, try to get from global app instance (for internal calls)
      if (!io && global.app) {
        io = global.app.get('io');
      }

      if (io) {
        // Broadcast to all mechanics via Socket.IO
        io.emit('new_service_request', notificationData);
        console.log('📡 Service request broadcasted to all mechanics via Socket.IO');
      } else {
        console.log('⚠️ Socket.IO not available - skipping real-time broadcast');
      }

      // Always try to send FCM notifications (works for both internal and API calls)
      try {
        await pushNotificationService.notifyAllMechanicsNewRequest(
          driver ? driver.name : 'A driver',
          issue_type,
          issue_description,
          truck_info,
          serviceRequest.id,
          { latitude, longitude },
          {
            driver_phone: driver ? driver.phone : null,
          }
        );
        console.log('📱 FCM notifications sent to mechanics');
      } catch (fcmError) {
        console.error('❌ FCM broadcast failed:', fcmError);
      }

      // Handle response based on call type
      if (isInternalCall) {
        // For internal calls, just return the service request
        return serviceRequest;
      } else {
        // For API calls, send JSON response
        res.status(201).json({
          message: 'Service request created successfully',
          serviceRequest: serviceRequest,
        });
      }
    } catch (error) {
      console.error("Error creating service request:", error);

      if (isInternalCall) {
        // For internal calls, throw the error
        throw error;
      } else {
        // For API calls, send error response
        res.status(500).json({ message: "Failed to create service request" });
      }
    }
  },

  // Legacy create function for backward compatibility
  createServiceRequest: async (data) => {
    try {
      const {
        issue_type,
        truck_info,
        issue_description,
        latitude,
        longitude,
        status,
        driver_id,
      } = data;

      const serviceRequest = await ServiceRequest.create({
        issue_type,
        truck_info,
        issue_description,
        latitude,
        longitude,
        status,
        driver_id,
      });

      return serviceRequest;
    } catch (error) {
      console.error("Error creating service request:", error);
      throw error;
    }
  },

  // GET /api/requests
  findAll: async (req, res) => {
    try {
      const { status, companyId } = req.query;
      const filter = {};
      if (status) filter.status = status;
      if (companyId) filter.transport_company_id = companyId;

      const requests = await ServiceRequest.findAll({ where: filter });
      res.json(requests);
    } catch (error) {
      console.error("Error fetching service requests:", error);
      res.status(500).json({ message: "Failed to fetch service requests" });
    }
  },

  // GET /api/requests/:id
  findOne: async (req, res) => {
    try {
      const { id } = req.params;
      const request = await ServiceRequest.findByPk(id);
      if (!request)
        return res.status(404).json({ message: "Service request not found" });
      res.json(request);
    } catch (error) {
      console.error("Error fetching service request:", error);
      res.status(500).json({ message: "Failed to fetch service request" });
    }
  },

  // PUT /api/requests/:id/status
  updateStatus: async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;

      console.log(`🔄 Backend: Updating service request ${id} status to: ${status}`);
      console.log(`🔄 Backend: Request user:`, req.user?.id, req.user?.role);
      console.log(`🔄 Backend: Request body:`, req.body);

      const request = await ServiceRequest.findByPk(id, {
        include: [
          { model: db.Driver, as: "Driver" },
          { model: db.ServiceAssignment, include: [{ model: db.Mechanic }] }
        ]
      });

      console.log(`🔍 Backend: Service request found:`, request ? 'YES' : 'NO');
      if (request) {
        console.log(`🔍 Backend: Service request details:`, {
          id: request.id,
          status: request.status,
          driver_id: request.driver_id,
          created_at: request.created_at
        });
      }

      if (!request)
        return res.status(404).json({ message: "Service request not found" });

      const oldStatus = request.status;
      request.status = status;
      if (status === "assigned") request.assigned_at = new Date();
      if (status === "completed") request.completed_at = new Date();
      // Note: cancelled_at field doesn't exist in ServiceRequest model yet

      await request.save();

      // Emit real-time status update
      const io = req.app.get('io');
      if (io) {
        io.to(`service_request_${id}`).emit('status_updated', {
          service_request_id: id,
          old_status: oldStatus,
          new_status: status,
          updated_at: new Date(),
        });
      }

      res.json(request);
    } catch (error) {
      console.error("Error updating service request status:", error);
      res.status(500).json({ message: "Failed to update status" });
    }
  },

  // GET /api/requests/available - Get available service requests for mechanics
  getAvailableForMechanics: async (req, res) => {
    try {
      const mechanic_id = req.user.id;

      // Get mechanic info (simplified for now)
      const mechanic = await Mechanic.findByPk(mechanic_id);

      if (!mechanic) {
        return res.status(404).json({ message: "Mechanic not found" });
      }

      // Get available service requests (pending status, simplified query)
      const availableRequests = await ServiceRequest.findAll({
        where: {
          status: 'pending'
        },
        include: [
          {
            model: Driver,
            as: "Driver",
            attributes: ['id', 'name', 'phone', 'last_location_lat', 'last_location_lng']
          }
        ],
        order: [['createdAt', 'DESC']]
      });

      console.log(`📋 Found ${availableRequests.length} available service requests for mechanic ${mechanic_id}`);

      res.json({
        requests: availableRequests,
        mechanic: {
          id: mechanic.id,
          name: mechanic.name
        }
      });
    } catch (error) {
      console.error("Error fetching available service requests:", error);
      res.status(500).json({ message: "Failed to fetch available service requests" });
    }
  },

  // POST /api/requests/request-mechanics
};

export default serviceRequestController;

// Integration in server.js
// import serviceRequestRoutes from './routes/serviceRequest.routes.js';
// app.use('/api/requests', serviceRequestRoutes);
