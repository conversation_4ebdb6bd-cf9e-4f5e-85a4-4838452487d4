// middlewares/auth.middleware.js
import jwt from "jsonwebtoken";
import db from "../models/index.js";

const JWT_SECRET = process.env.JWT_SECRET || "your_jwt_secret";
const { Driver, Mechanic, TransportCompany, TruckRepairCompany, OwnerAdmin } = db;

export const verifyToken = (req, res, next) => {
  const authHeader = req.headers.authorization;

  console.log("🔐 Backend Auth: Checking authorization header:", authHeader ? "EXISTS" : "MISSING");

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    console.log("❌ Backend Auth: No valid authorization header");
    return res.status(401).json({ message: "Unauthorized: No token provided" });
  }

  const token = authHeader.split(" ")[1];
  console.log("🔐 Backend Auth: Extracted token:", token ? "EXISTS" : "MISSING");

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log("✅ Backend Auth: Token verified successfully for user:", decoded.id, decoded.role);
    req.user = decoded; // { id, role }
    next();
  } catch (err) {
    console.log("❌ Backend Auth: Token verification failed:", err.message);
    return res.status(401).json({ message: "Unauthorized: Invalid token" });
  }
};

// Enhanced token verification with user data
export const verifyTokenWithUserData = async (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ message: "Unauthorized: No token provided" });
  }

  const token = authHeader.split(" ")[1];

  try {
    const decoded = jwt.verify(token, JWT_SECRET);

    // Fetch full user data based on role
    let userData = null;
    switch (decoded.role) {
      case "driver":
        userData = await Driver.findByPk(decoded.id, {
          include: [{ model: TransportCompany, as: "company" }]
        });
        break;
      case "mechanic":
        userData = await Mechanic.findByPk(decoded.id, {
          include: [{ model: TruckRepairCompany, as: "company" }]
        });
        break;
      case "transport_company":
        userData = await TransportCompany.findByPk(decoded.id);
        break;
      case "truck_repair_company":
        userData = await TruckRepairCompany.findByPk(decoded.id);
        break;
      case "owner_admin":
      case "super_admin":
        userData = await OwnerAdmin.findByPk(decoded.id);
        break;
    }

    if (!userData) {
      return res.status(401).json({ message: "User not found" });
    }

    req.user = {
      ...decoded,
      userData,
      companyId: userData.transport_company_id || userData.truck_repair_company_id || userData.id,
    };

    next();
  } catch (err) {
    return res.status(401).json({ message: "Unauthorized: Invalid token" });
  }
};
