import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { driverService } from "../../api/services";

// Async thunks for service operations
export const createServiceRequest = createAsyncThunk(
  "service/createRequest",
  async ({ requestData, token }, { rejectWithValue }) => {
    try {
      const response = await driverService.requestService(requestData);
      return response.data;
    } catch (error) {
      console.error(
        "❌ Driver App: Failed to create service request:",
        error.response?.data || error.message
      );
      return rejectWithValue(
        error.response?.data?.message || "Failed to create service request"
      );
    }
  }
);

export const loadServiceHistory = createAsyncThunk(
  "service/loadHistory",
  async (token, { rejectWithValue }) => {
    try {
      const response = await driverService.getServiceHistory();
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to load service history"
      );
    }
  }
);

export const loadServiceDetails = createAsyncThunk(
  "service/loadDetails",
  async ({ serviceId, token }, { rejectWithValue }) => {
    try {
      const response = await driverService.getServiceDetails(serviceId);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to load service details"
      );
    }
  }
);

export const updateServiceRequestStatus = createAsyncThunk(
  "service/updateStatus",
  async ({ serviceId, status, token }, { rejectWithValue }) => {
    try {
      const response = await driverService.updateServiceStatus(
        serviceId,
        status
      );
      return { serviceId, status, ...response.data };
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to update service status"
      );
    }
  }
);

export const cancelServiceRequestAPI = createAsyncThunk(
  "service/cancelRequest",
  async ({ serviceId }, { rejectWithValue }) => {
    try {
      console.log("🚫 API: Cancelling service request:", serviceId);
      const response = await driverService.updateServiceStatus(
        serviceId,
        "cancelled"
      );
      console.log("✅ API: Service request cancelled successfully");
      return { serviceId, status: "cancelled", ...response.data };
    } catch (error) {
      console.error("❌ API: Failed to cancel service request:", error);
      return rejectWithValue(
        error.response?.data?.message || "Failed to cancel service request"
      );
    }
  }
);

export const submitServiceRating = createAsyncThunk(
  "service/submitRating",
  async ({ serviceId, rating, review, token }, { rejectWithValue }) => {
    try {
      const response = await driverService.rateService(
        serviceId,
        rating,
        review
      );
      return { serviceId, rating, review, ...response.data };
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to submit rating"
      );
    }
  }
);

const initialState = {
  // Current active service request
  activeRequest: null,

  // Mechanic location for active service
  mechanicLocation: null,

  // Service history - will be populated from backend
  serviceHistory: [],

  // Currently selected service details
  selectedService: null,

  // Waiting state for mechanic acceptance
  waitingForMechanic: false,
  waitingStartTime: null,
  estimatedWaitTime: 5, // minutes

  // Loading states
  loading: {
    creating: false,
    loadingHistory: false,
    loadingDetails: false,
    updating: false,
    rating: false,
  },

  // Error states
  error: {
    create: null,
    history: null,
    details: null,
    update: null,
    rating: null,
  },

  // Filters and pagination
  filters: {
    status: "all", // all, pending, completed, cancelled
    serviceType: "all", // all, tire, engine, tow
    dateRange: "all", // all, today, week, month
  },

  // Statistics
  stats: {
    totalServices: 0,
    completedServices: 0,
    pendingServices: 0,
    cancelledServices: 0,
    averageRating: 0,
    totalEarnings: 0,
  },
};

const serviceSlice = createSlice({
  name: "service",
  initialState,
  reducers: {
    // Clear errors
    clearErrors: (state) => {
      state.error = {
        create: null,
        history: null,
        details: null,
        update: null,
        rating: null,
      };
    },

    // Set filters
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },

    // Clear selected service
    clearSelectedService: (state) => {
      state.selectedService = null;
    },

    // Update active request
    setActiveRequest: (state, action) => {
      state.activeRequest = action.payload;
    },

    // Clear active request
    clearActiveRequest: (state) => {
      state.activeRequest = null;
      state.mechanicLocation = null; // Clear mechanic location when clearing active request
    },

    // Update mechanic location
    updateMechanicLocation: (state, action) => {
      state.mechanicLocation = action.payload;
    },

    // Clear mechanic location
    clearMechanicLocation: (state) => {
      state.mechanicLocation = null;
    },

    // Update service in history
    updateServiceInHistory: (state, action) => {
      const { serviceId, updates } = action.payload;
      const serviceIndex = state.serviceHistory.findIndex(
        (service) => service.id === serviceId
      );
      if (serviceIndex !== -1) {
        state.serviceHistory[serviceIndex] = {
          ...state.serviceHistory[serviceIndex],
          ...updates,
        };
      }
    },

    // Add mock service for testing
    addMockService: (state, action) => {
      const service = action.payload;
      const existingIndex = state.serviceHistory.findIndex(
        (s) => s.id === service.id
      );
      if (existingIndex === -1) {
        state.serviceHistory.push(service);
      }
    },

    // Waiting state management
    setWaitingForMechanic: (state, action) => {
      state.waitingForMechanic = action.payload;
      if (action.payload) {
        state.waitingStartTime = new Date().toISOString();
      } else {
        state.waitingStartTime = null;
      }
    },

    setEstimatedWaitTime: (state, action) => {
      state.estimatedWaitTime = action.payload;
    },

    // Handle mechanic acceptance
    handleMechanicAccepted: (state, action) => {
      state.waitingForMechanic = false;
      state.waitingStartTime = null;
      if (state.activeRequest) {
        state.activeRequest.status = "assigned";
        // Update mechanic info with normalized structure
        const { mechanic, estimatedArrival } = action.payload;
        state.activeRequest.mechanicId = mechanic?.id;
        state.activeRequest.mechanicName = mechanic?.name;
        state.activeRequest.mechanicPhone = mechanic?.phone;
        state.activeRequest.estimatedArrival = estimatedArrival;
        state.activeRequest.assignedAt = new Date().toISOString();

        // Store mechanic location if provided
        if (mechanic?.location) {
          state.activeRequest.mechanicLocation = {
            latitude: mechanic.location.latitude,
            longitude: mechanic.location.longitude,
            lastUpdated: mechanic.location.lastUpdated,
          };

          // Also update the global mechanic location
          state.mechanicLocation = {
            latitude: parseFloat(mechanic.location.latitude),
            longitude: parseFloat(mechanic.location.longitude),
            lastUpdated: mechanic.location.lastUpdated,
          };
        }
      }
    },

    // Cancel service request (local only - for immediate UI update)
    cancelServiceRequest: (state) => {
      console.log("🚫 Redux: Cancelling service request - clearing all states");
      state.activeRequest = null;
      state.waitingForMechanic = false;
      state.waitingStartTime = null;
      state.mechanicLocation = null;
      // CRITICAL FIX: Clear any additional service-related states
      state.estimatedWaitTime = null;
      state.selectedService = null;
    },

    // Reset all state to initial values (for testing)
    resetServiceState: (state) => {
      state.activeRequest = null;
      state.mechanicLocation = null;
      state.waitingForMechanic = false;
      state.waitingStartTime = null;
      state.estimatedWaitTime = 5;
      state.selectedService = null;
      state.loading = {
        creating: false,
        loadingHistory: false,
        loadingDetails: false,
        updating: false,
        rating: false,
      };
      state.error = {
        create: null,
        load: null,
        update: null,
        rating: null,
      };
    },
  },
  extraReducers: (builder) => {
    // Create service request
    builder
      .addCase(createServiceRequest.pending, (state) => {
        state.loading.creating = true;
        state.error.create = null;
      })
      .addCase(createServiceRequest.fulfilled, (state, action) => {
        state.loading.creating = false;

        // Normalize the activeRequest structure
        const { serviceRequest, driver, message } = action.payload;

        if (serviceRequest) {
          // Set activeRequest with normalized structure
          state.activeRequest = {
            id: serviceRequest.id,
            status: serviceRequest.status,
            issueType: serviceRequest.issue_type,
            issueDescription: serviceRequest.issue_description,
            vehicleDetails: serviceRequest.vehicle_details,
            location: {
              latitude: serviceRequest.latitude,
              longitude: serviceRequest.longitude,
            },
            createdAt: serviceRequest.createdAt,
            updatedAt: serviceRequest.updatedAt,
            assignedAt: serviceRequest.assigned_at,
            completedAt: serviceRequest.completed_at,
            // Driver info
            driverId: driver?.id,
            driverName: driver?.name,
            driverEmail: driver?.email,
            // Mechanic info (initially null)
            mechanicId: null,
            mechanicName: null,
            mechanicPhone: null,
            estimatedArrival: null,
            // Metadata
            message: message,
          };

          // ALWAYS set waiting state for new service requests - regardless of status
          // This ensures the waiting UI shows immediately
          state.waitingForMechanic = true;
          state.waitingStartTime = new Date().toISOString();

          // Add to history if not already there
          const existingIndex = state.serviceHistory.findIndex(
            (service) => service.id === serviceRequest.id
          );
          if (existingIndex === -1) {
            state.serviceHistory.unshift(serviceRequest);
          }
        } else {
          // Fallback for unexpected payload structure
          state.activeRequest = action.payload;
        }
      })
      .addCase(createServiceRequest.rejected, (state, action) => {
        state.loading.creating = false;
        state.error.create = action.payload;
      });

    // Load service history
    builder
      .addCase(loadServiceHistory.pending, (state) => {
        state.loading.loadingHistory = true;
        state.error.history = null;
      })
      .addCase(loadServiceHistory.fulfilled, (state, action) => {
        state.loading.loadingHistory = false;
        state.serviceHistory = action.payload.services || [];
        state.stats = action.payload.stats || state.stats;
      })
      .addCase(loadServiceHistory.rejected, (state, action) => {
        state.loading.loadingHistory = false;
        state.error.history = action.payload;
      });

    // Load service details
    builder
      .addCase(loadServiceDetails.pending, (state) => {
        state.loading.loadingDetails = true;
        state.error.details = null;
      })
      .addCase(loadServiceDetails.fulfilled, (state, action) => {
        state.loading.loadingDetails = false;
        state.selectedService = action.payload;
      })
      .addCase(loadServiceDetails.rejected, (state, action) => {
        state.loading.loadingDetails = false;
        state.error.details = action.payload;
      });

    // Update service status
    builder
      .addCase(updateServiceRequestStatus.pending, (state) => {
        state.loading.updating = true;
        state.error.update = null;
      })
      .addCase(updateServiceRequestStatus.fulfilled, (state, action) => {
        state.loading.updating = false;
        const { serviceId, status } = action.payload;

        // Update in history
        const serviceIndex = state.serviceHistory.findIndex(
          (service) => service.id === serviceId
        );
        if (serviceIndex !== -1) {
          state.serviceHistory[serviceIndex].status = status;
        }

        // Update selected service if it's the same
        if (state.selectedService?.id === serviceId) {
          state.selectedService.status = status;
        }

        // Update active request if it's the same
        if (state.activeRequest?.id === serviceId) {
          state.activeRequest.status = status;
        }
      })
      .addCase(updateServiceRequestStatus.rejected, (state, action) => {
        state.loading.updating = false;
        state.error.update = action.payload;
      });

    // Submit service rating
    builder
      .addCase(submitServiceRating.pending, (state) => {
        state.loading.rating = true;
        state.error.rating = null;
      })
      .addCase(submitServiceRating.fulfilled, (state, action) => {
        state.loading.rating = false;
        const { serviceId, rating, review } = action.payload;

        // Update in history
        const serviceIndex = state.serviceHistory.findIndex(
          (service) => service.id === serviceId
        );
        if (serviceIndex !== -1) {
          state.serviceHistory[serviceIndex].rating = rating;
          state.serviceHistory[serviceIndex].review = review;
        }

        // Update selected service if it's the same
        if (state.selectedService?.id === serviceId) {
          state.selectedService.rating = rating;
          state.selectedService.review = review;
        }
      })
      .addCase(submitServiceRating.rejected, (state, action) => {
        state.loading.rating = false;
        state.error.rating = action.payload;
      })

      // Cancel service request API
      .addCase(cancelServiceRequestAPI.pending, (state) => {
        state.loading.updating = true;
        state.error.update = null;
      })
      .addCase(cancelServiceRequestAPI.fulfilled, (state, action) => {
        state.loading.updating = false;
        console.log("✅ Redux: Service cancellation API completed - clearing all states");

        // CRITICAL FIX: Clear ALL active request and waiting states
        state.activeRequest = null;
        state.waitingForMechanic = false;
        state.waitingStartTime = null;
        state.mechanicLocation = null;
        state.estimatedWaitTime = null;
        state.selectedService = null;

        // Update service in history with cancelled status
        const serviceIndex = state.serviceHistory.findIndex(
          (service) => service.id === action.payload.serviceId
        );
        if (serviceIndex !== -1) {
          state.serviceHistory[serviceIndex] = {
            ...state.serviceHistory[serviceIndex],
            status: "cancelled",
            cancelledAt: new Date().toISOString(),
          };
        }
      })
      .addCase(cancelServiceRequestAPI.rejected, (state, action) => {
        state.loading.updating = false;
        state.error.update = action.payload;
      });
  },
});

export const {
  clearErrors,
  setFilters,
  clearSelectedService,
  setActiveRequest,
  clearActiveRequest,
  updateMechanicLocation,
  clearMechanicLocation,
  updateServiceInHistory,
  addMockService,
  setWaitingForMechanic,
  setEstimatedWaitTime,
  handleMechanicAccepted,
  cancelServiceRequest,
  resetServiceState,
} = serviceSlice.actions;

// Selectors
export const selectActiveRequest = (state) => state.service.activeRequest;
export const selectMechanicLocation = (state) => state.service.mechanicLocation;
export const selectServiceHistory = (state) => state.service.serviceHistory;
export const selectSelectedService = (state) => state.service.selectedService;
export const selectServiceLoading = (state) => state.service.loading;
export const selectServiceErrors = (state) => state.service.error;
export const selectServiceFilters = (state) => state.service.filters;

// Waiting state selectors
export const selectWaitingForMechanic = (state) =>
  state.service.waitingForMechanic;
export const selectWaitingStartTime = (state) => state.service.waitingStartTime;
export const selectEstimatedWaitTime = (state) =>
  state.service.estimatedWaitTime;
export const selectServiceStats = (state) => state.service.stats;

// Simple filtered service history selector
export const selectFilteredServiceHistory = (state) => {
  const { serviceHistory, filters } = state.service;

  if (!serviceHistory || !filters) {
    return [];
  }

  return serviceHistory.filter((service) => {
    if (filters.status === "all") return true;
    return service.status === filters.status;
  });
};

export default serviceSlice.reducer;
