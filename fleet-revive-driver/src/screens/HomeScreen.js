import React, { useEffect, useRef, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Image,
  TextInput,
  Alert,
  ActivityIndicator,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import EnhancedMapView from "../components/EnhancedMapView";
import ChatOverlay from "../components/ChatOverlay";
import ConnectionStatusIndicator from "../components/ConnectionStatusIndicator";
import ServiceRequestWaitingUI from "../components/ServiceRequestWaitingUI";
import ActionSheet from "react-native-actions-sheet";
import { pickImage, takePhoto } from "../utils/helper";
import locationService from "../services/locationService";
import socketService from "../services/socketService";
import { useTheme } from "../context/ThemeContext";
import { useSelector, useDispatch } from "react-redux";
import { selectToken, selectUser } from "../store/slices/authSlice";
import {
  createServiceRequest,
  selectServiceLoading,
  selectActiveRequest,
  selectMechanicLocation,
  updateMechanicLocation,
  setActiveRequest,
  selectWaitingForMechanic,
  selectEstimatedWaitTime,
  setWaitingForMechanic,
  handleMechanicAccepted,
  cancelServiceRequest,
  cancelServiceRequestAPI,
} from "../store/slices/serviceSlice";
import { API_CONFIG } from "../config/constants";

const serviceButtons = ["Tire", "Engine", "Tow"];
const { width } = Dimensions.get("window");
const safeWidth = width && !isNaN(width) ? width : 375; // Fallback to iPhone width

export default function App() {
  const [location, setLocation] = useState(null);
  const [serviceType, setServiceType] = useState(null);
  const [images, setImages] = useState([]);
  const [description, setDescription] = useState("");
  const [vehicleDetails, setVehicleDetails] = useState("");
  const [statusText, setStatusText] = useState("Submit");
  const [showChat, setShowChat] = useState(false);
  const [chatMinimized, setChatMinimized] = useState(false);
  const [minimumWaitTimeActive, setMinimumWaitTimeActive] = useState(false);
  const [autoCloseTimer, setAutoCloseTimer] = useState(null);
  const [isCancellingService, setIsCancellingService] = useState(false);
  const { theme } = useTheme();
  const actionSheetRef = useRef(null);
  const dispatch = useDispatch();

  // Safety check for theme to prevent NaN values
  const safeTheme = theme || {
    colors: {
      border: "#C6C6C8",
      text: "#000000",
      surface: "#F2F2F7",
      textTertiary: "#8E8E93",
      primary: "#007AFF",
      textPrimary: "#000000",
    },
  };

  // Create styles with current theme
  const styles = createStyles(theme);

  // Redux selectors
  const token = useSelector(selectToken);
  const user = useSelector(selectUser);
  const serviceLoading = useSelector(selectServiceLoading);
  const activeRequest = useSelector(selectActiveRequest);
  const mechanicLocation = useSelector(selectMechanicLocation);
  const waitingForMechanic = useSelector(selectWaitingForMechanic);
  const estimatedWaitTime = useSelector(selectEstimatedWaitTime);

  // Use Redux loading state instead of local isSubmitting
  const isSubmitting = serviceLoading?.creating || false;

  // Check if user is authenticated and initialize WebSocket
  useEffect(() => {
    if (!user || !token) {
      console.log("User not authenticated, should show login screen");
    } else {
      console.log("User authenticated:", user.name);
      console.log("Full user object:", JSON.stringify(user, null, 2));
      console.log("Token exists:", !!token);

      // Initialize Socket.IO connection for real-time updates
      socketService.connect();

      // Join service room if there's an active request
      if (activeRequest?.id) {
        socketService.joinServiceRoom(activeRequest.id);
      }

      // Set up socket event listeners for UI-specific updates
      const handleMechanicAssigned = (data) => {
        console.log("🔧 Driver App: Mechanic assigned - UI update:", data);
        // Show chat when mechanic is assigned
        setShowChat(true);
        setChatMinimized(true);

        // CRITICAL: Join service room for location updates
        if (data.serviceId) {
          console.log("🏠 Driver: Joining service room for location updates:", data.serviceId);
          socketService.joinServiceRoom(data.serviceId);

          // Request initial mechanic location
          console.log("📍 Driver: Requesting initial mechanic location");
          socketService.emit('request_mechanic_location', { serviceId: data.serviceId });

          // Fallback: Try to get mechanic location from API after a delay
          setTimeout(async () => {
            const currentMechanicLocation = mechanicLocation;
            if (!currentMechanicLocation && data.serviceId) {
              console.log("📍 Driver: Socket location request timeout, trying API fallback");
              try {
                const response = await fetch(`${API_CONFIG.BASE_URL}/location/service-request/${data.serviceId}`, {
                  headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                  },
                });

                if (response.ok) {
                  const locationData = await response.json();
                  console.log("📍 Driver: Got location data from API:", locationData);

                  // Extract mechanic location from the response
                  const mechanicLocation = locationData.mechanic;
                  if (mechanicLocation && mechanicLocation.latitude && mechanicLocation.longitude) {
                    console.log("📍 Driver: Updating mechanic location from API:", mechanicLocation);
                    dispatch(updateMechanicLocation({
                      latitude: parseFloat(mechanicLocation.latitude),
                      longitude: parseFloat(mechanicLocation.longitude),
                      timestamp: mechanicLocation.lastUpdated || new Date().toISOString(),
                    }));
                  } else {
                    console.log("⚠️ Driver: No mechanic location found in API response");
                  }
                }
              } catch (error) {
                console.error("❌ Driver: Failed to get mechanic location from API:", error);
              }
            }
          }, 3000); // Wait 3 seconds for socket response
        }
      };

      const handleServiceStatusUpdate = (data) => {
        console.log("🔄 Driver App: Service status update - UI update:", data);
        // Handle UI-specific updates based on status
        if (data.status === "completed") {
          setShowChat(false);
          setChatMinimized(false);
        }
      };

      const handleServiceCompleted = (data) => {
        console.log("✅ Driver App: Service completed - UI update:", data);
        setShowChat(false);
        setChatMinimized(false);

        // Show completion notification
        Alert.alert(
          "Service Completed! 🎉",
          "Your service request has been completed by the mechanic.",
          [{ text: "OK" }]
        );
      };

      const handleServiceCancelled = (data) => {
        console.log("🚫 Driver App: Service cancelled - UI update:", data);
        setShowChat(false);
        setChatMinimized(false);

        // Show cancellation notification
        Alert.alert(
          "Service Cancelled",
          `Your service request has been cancelled. ${data.reason || ""}`,
          [{ text: "OK" }]
        );
      };

      // CRITICAL: Handle mechanic location updates
      const handleMechanicLocationUpdate = (data) => {
        if (data.userType === "mechanic" && activeRequest?.id === data.serviceRequestId) {
          // CRITICAL FIX: Throttle location update logs to prevent spam
          const now = Date.now();
          const logKey = `mechanic_location_${data.serviceRequestId}`;

          if (!window.lastLocationLogTimes) window.lastLocationLogTimes = {};
          const lastLogTime = window.lastLocationLogTimes[logKey] || 0;

          // Only log every 5 seconds to prevent spam
          if (now - lastLogTime > 5000) {
            console.log(`📍 Driver App: Mechanic location update for service ${data.serviceRequestId} (${new Date().toLocaleTimeString()})`);
            window.lastLocationLogTimes[logKey] = now;
          }

          dispatch(updateMechanicLocation({
            latitude: data.latitude,
            longitude: data.longitude,
            heading: data.heading,
            speed: data.speed,
            timestamp: data.timestamp || new Date().toISOString(),
          }));
        }
      };

      // Add socket event listeners for UI-specific updates
      socketService.addEventListener(
        "mechanic_assigned",
        handleMechanicAssigned
      );
      socketService.addEventListener(
        "service_status_update",
        handleServiceStatusUpdate
      );
      socketService.addEventListener(
        "service_completed",
        handleServiceCompleted
      );
      socketService.addEventListener(
        "service_cancelled",
        handleServiceCancelled
      );

      // CRITICAL: Listen for mechanic location updates
      socketService.addEventListener("location_update", handleMechanicLocationUpdate);

      return () => {
        // Clean up socket event listeners
        socketService.removeEventListener(
          "mechanic_assigned",
          handleMechanicAssigned
        );
        socketService.removeEventListener(
          "service_status_update",
          handleServiceStatusUpdate
        );
        socketService.removeEventListener(
          "service_completed",
          handleServiceCompleted
        );
        socketService.removeEventListener(
          "service_cancelled",
          handleServiceCancelled
        );
        socketService.removeEventListener(
          "location_update",
          handleMechanicLocationUpdate
        );
      };
    }

    return () => {
      // Clean up Socket.IO connection when component unmounts
      socketService.disconnect();
    };
  }, [user, token, activeRequest?.id, dispatch]);

  // Handle minimum wait time for better UX
  useEffect(() => {
    if (waitingForMechanic && !minimumWaitTimeActive) {
      setMinimumWaitTimeActive(true);

      const timer = setTimeout(() => {
        setMinimumWaitTimeActive(false);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [waitingForMechanic, minimumWaitTimeActive]);

  // Auto-close waiting UI if no mechanic accepts within 5 minutes
  useEffect(() => {
    if (waitingForMechanic && activeRequest?.status === "pending") {
      const timer = setTimeout(() => {
        Alert.alert(
          "Request Still Open",
          "No mechanic has accepted your request yet, but it remains open for mechanics to bid on. You'll be notified when someone accepts.",
          [
            {
              text: "Keep Waiting",
              style: "default",
            },
            {
              text: "Cancel Request",
              style: "destructive",
              onPress: async () => {
                try {
                  // Clear all waiting states immediately
                  dispatch(cancelServiceRequest());
                  setMinimumWaitTimeActive(false);

                  // Clear the auto-close timer
                  if (autoCloseTimer) {
                    clearTimeout(autoCloseTimer);
                    setAutoCloseTimer(null);
                  }

                  // Update database if we have an active request
                  if (activeRequest?.id) {
                    await dispatch(
                      cancelServiceRequestAPI({
                        serviceId: activeRequest.id,
                      })
                    );
                  }

                  Alert.alert(
                    "Request Cancelled",
                    "Your service request has been cancelled."
                  );
                } catch (error) {
                  console.error("❌ Error cancelling request:", error);
                  Alert.alert(
                    "Cancel Error",
                    "Request was cancelled locally but there was an issue updating the server."
                  );
                }
              },
            },
          ]
        );

        dispatch(setWaitingForMechanic(false));
      }, 5 * 60 * 1000); // 5 minutes

      setAutoCloseTimer(timer);

      return () => {
        if (timer) {
          clearTimeout(timer);
          setAutoCloseTimer(null);
        }
      };
    }
  }, [waitingForMechanic, activeRequest?.status, dispatch]);

  // Show chat when there's an active request with accepted status
  useEffect(() => {
    console.log("🔍 DRIVER: activeRequest changed:", {
      hasActiveRequest: !!activeRequest,
      activeRequestId: activeRequest?.id,
      activeRequestStatus: activeRequest?.status,
      mechanicId: activeRequest?.mechanicId,
      mechanicName: activeRequest?.mechanicName,
      mechanicLocation: activeRequest?.mechanicLocation,
      shouldShowChat:
        activeRequest &&
        (activeRequest.status === "accepted" ||
          activeRequest.status === "assigned"),
    });

    if (
      activeRequest &&
      (activeRequest.status === "accepted" ||
        activeRequest.status === "assigned")
    ) {
      console.log("💬 DRIVER: Showing chat for accepted/assigned request");
      setShowChat(true);
    } else {
      console.log("💬 DRIVER: Hiding chat - no active request or not accepted");
      setShowChat(false);
      setChatMinimized(false);
    }
  }, [activeRequest]);

  // CRITICAL FIX: Clear waiting state when connected to mechanic
  useEffect(() => {
    if (activeRequest && (activeRequest.status === "assigned" || activeRequest.status === "accepted")) {
      console.log("🔧 DRIVER: Connected to mechanic - clearing waiting state");
      dispatch(setWaitingForMechanic(false));
      setMinimumWaitTimeActive(false);

      // Clear any auto-close timer
      if (autoCloseTimer) {
        clearTimeout(autoCloseTimer);
        setAutoCloseTimer(null);
      }

      // CRITICAL: Store active request for persistence
      AsyncStorage.setItem("activeServiceRequest", JSON.stringify(activeRequest))
        .then(() => console.log("💾 DRIVER: Active request stored for persistence"))
        .catch(error => console.error("❌ DRIVER: Failed to store active request:", error));

      // CRITICAL: Ensure we're in the service room for location updates
      if (activeRequest.id && user?.id && token) {
        console.log("🏠 DRIVER: Ensuring service room connection for active request:", activeRequest.id);
        socketService.joinServiceRoom(activeRequest.id);

        // Request current mechanic location
        console.log("📍 DRIVER: Requesting current mechanic location for active request");
        socketService.emit('request_mechanic_location', { serviceId: activeRequest.id });

        // Fallback: Try to get mechanic location from API after a delay
        setTimeout(async () => {
          if (!mechanicLocation && activeRequest?.id) {
            console.log("📍 DRIVER: Socket location request timeout, trying API fallback");
            try {
              const response = await fetch(`${API_CONFIG.BASE_URL}/location/service-request/${activeRequest.id}`, {
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json',
                },
              });

              if (response.ok) {
                const locationData = await response.json();
                console.log("📍 DRIVER: Got location data from API:", locationData);

                // Extract mechanic location from the response
                const mechanicLocation = locationData.mechanic;
                if (mechanicLocation && mechanicLocation.latitude && mechanicLocation.longitude) {
                  console.log("📍 DRIVER: Updating mechanic location from API:", mechanicLocation);
                  dispatch(updateMechanicLocation({
                    latitude: parseFloat(mechanicLocation.latitude),
                    longitude: parseFloat(mechanicLocation.longitude),
                    timestamp: mechanicLocation.lastUpdated || new Date().toISOString(),
                  }));
                } else {
                  console.log("⚠️ DRIVER: No mechanic location found in API response");
                }
              }
            } catch (error) {
              console.error("❌ DRIVER: Failed to get mechanic location from API:", error);
            }
          }
        }, 3000); // Wait 3 seconds for socket response
      }
    } else if (!activeRequest) {
      // Clear stored active request when there's no active request
      AsyncStorage.removeItem("activeServiceRequest")
        .then(() => console.log("🗑️ DRIVER: Cleared stored active request"))
        .catch(error => console.error("❌ DRIVER: Failed to clear stored active request:", error));
    }
  }, [activeRequest?.status, activeRequest?.id, dispatch, autoCloseTimer, user?.id, token, mechanicLocation]);

  // Handle service cancellation
  const handleCancelService = async () => {
    if (!activeRequest?.id) return;

    Alert.alert(
      "Cancel Service",
      "Are you sure you want to cancel this service request? This action cannot be undone.",
      [
        {
          text: "No, Keep Service",
          style: "cancel",
        },
        {
          text: "Yes, Cancel Service",
          style: "destructive",
          onPress: async () => {
            setIsCancellingService(true);
            try {
              console.log(
                "🚫 Driver: Cancelling service request:",
                activeRequest.id
              );
              console.log(
                "🚫 Driver: Active request object:",
                JSON.stringify(activeRequest, null, 2)
              );

              // Check if user is authenticated
              const token = await AsyncStorage.getItem("token");
              const userData = await AsyncStorage.getItem("user");
              console.log("🔑 Driver: Token exists:", token ? "YES" : "NO");
              console.log(
                "👤 Driver: User data exists:",
                userData ? "YES" : "NO"
              );

              if (!token) {
                Alert.alert(
                  "Authentication Required",
                  "Please log in again to cancel the service request.",
                  [{ text: "OK" }]
                );
                return;
              }

              // Test authentication first with a simple API call
              try {
                console.log("🔍 Driver: Testing authentication...");
                const testResponse = await fetch(
                  `${API_CONFIG.BASE_URL}/requests`,
                  {
                    method: "GET",
                    headers: {
                      Authorization: `Bearer ${token}`,
                      "Content-Type": "application/json",
                    },
                  }
                );
                console.log(
                  "🔍 Driver: Auth test response status:",
                  testResponse.status
                );

                if (testResponse.status === 401) {
                  throw new Error("Authentication failed - token invalid");
                }
              } catch (authError) {
                console.error(
                  "❌ Driver: Authentication test failed:",
                  authError
                );
                Alert.alert(
                  "Authentication Required",
                  "Your session has expired. Please log in again.",
                  [{ text: "OK" }]
                );
                return;
              }

              // Cancel via API
              await dispatch(
                cancelServiceRequestAPI({
                  serviceId: activeRequest.id,
                })
              );

              // Show success message
              Alert.alert(
                "Service Cancelled",
                "Your service request has been cancelled successfully.",
                [{ text: "OK" }]
              );

              console.log("✅ Driver: Service request cancelled successfully");
            } catch (error) {
              console.error("❌ Driver: Failed to cancel service:", error);

              // Check if it's an authentication error
              if (error.message && error.message.includes("401")) {
                Alert.alert(
                  "Authentication Required",
                  "Your session has expired. Please log in again to cancel the service request.",
                  [
                    {
                      text: "OK",
                      onPress: () => {
                        // Clear stored auth data
                        AsyncStorage.multiRemove(["token", "user"]);
                        // Navigate to login (you might need to implement this navigation)
                        console.log(
                          "🚪 Driver: Should navigate to login screen"
                        );
                      },
                    },
                  ]
                );
              } else {
                Alert.alert(
                  "Cancellation Failed",
                  "Failed to cancel the service request. Please try again.",
                  [{ text: "OK" }]
                );
              }
            } finally {
              setIsCancellingService(false);
            }
          },
        },
      ]
    );
  };

  useEffect(() => {
    const initializeLocation = async () => {
      try {
        // Get current location
        const currentLocation = await locationService.getCurrentLocation();
        if (currentLocation) {
          setLocation(currentLocation);
        }

        // Set up location listener
        const handleLocationUpdate = (newLocation) => {
          if (newLocation?.coords?.latitude && newLocation?.coords?.longitude) {
            setLocation(newLocation);

            // Share location with mechanic if there's an active service request
            if (
              activeRequest?.id &&
              (activeRequest.status === "accepted" ||
                activeRequest.status === "assigned")
            ) {
              socketService.shareLocation(
                activeRequest.id,
                newLocation.coords.latitude,
                newLocation.coords.longitude,
                newLocation.coords.heading,
                newLocation.coords.speed
              );
            }
          }
        };

        locationService.addEventListener(
          "location_update",
          handleLocationUpdate
        );

        return () => {
          locationService.removeEventListener(
            "location_update",
            handleLocationUpdate
          );
        };
      } catch (error) {
        console.error("Error initializing location:", error);
        Alert.alert(
          "Location Error",
          "Unable to get your location. Please check your location settings."
        );
      }
    };

    initializeLocation();
  }, []);

  useEffect(() => {
    if (images.length > 3) {
      setImages(images.slice(0, 3));
      Alert.alert("Limit reached", "You can only upload up to 3 images.");
    }
  }, [images]);

  // Handle canceling service request
  const handleCancelServiceRequest = async () => {
    try {
      Alert.alert(
        "Cancel Request",
        "Are you sure you want to cancel this service request?",
        [
          { text: "No", style: "cancel" },
          {
            text: "Yes, Cancel",
            style: "destructive",
            onPress: async () => {
              try {
                // First clear UI states immediately for better UX
                dispatch(cancelServiceRequest());
                setMinimumWaitTimeActive(false);

                // Clear any auto-close timer
                if (autoCloseTimer) {
                  clearTimeout(autoCloseTimer);
                  setAutoCloseTimer(null);
                }

                // Then update database if we have an active request
                if (activeRequest?.id) {
                  await dispatch(
                    cancelServiceRequestAPI({
                      serviceId: activeRequest.id,
                    })
                  );
                }

                Alert.alert(
                  "Request Cancelled",
                  "Your service request has been cancelled."
                );
              } catch (error) {
                console.error("❌ Error cancelling request:", error);
                Alert.alert(
                  "Cancel Error",
                  "Request was cancelled locally but there was an issue updating the server. Please check your connection."
                );
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error("Error cancelling service request:", error);
      Alert.alert(
        "Error",
        "Failed to cancel service request. Please try again."
      );
    }
  };

  const handleWaitTimeExpired = () => {
    Alert.alert(
      "Extended Search",
      "We're still looking for available mechanics in your area. We'll notify you as soon as one becomes available.",
      [
        {
          text: "Keep Waiting",
          style: "default",
        },
        {
          text: "Cancel Request",
          style: "destructive",
          onPress: () => {
            dispatch(cancelServiceRequest());
            Alert.alert(
              "Request Cancelled",
              "Your service request has been cancelled."
            );
          },
        },
      ]
    );
  };

  // Handle mechanic acceptance
  const handleMechanicAcceptance = (mechanicData) => {
    dispatch(handleMechanicAccepted(mechanicData));
    Alert.alert(
      "Mechanic Found!",
      `${mechanicData.mechanic.name} has accepted your request and is on the way.`,
      [{ text: "OK" }]
    );
  };

  const handleSubmit = async () => {
    if (!serviceType) {
      Alert.alert("Service Type Required", "Please select a service type.");
      return;
    }
    if (!description.trim()) {
      Alert.alert("Description Required", "Please describe your issue.");
      return;
    }
    if (!vehicleDetails.trim()) {
      Alert.alert("Vehicle Details Required", "Please enter vehicle details.");
      return;
    }
    if (!location?.coords?.latitude || !location?.coords?.longitude) {
      Alert.alert(
        "Location Required",
        "Unable to get your location. Please check your location settings."
      );
      return;
    }

    setStatusText("Submitting...");

    try {
      const requestData = {
        driverId: user?.id,
        serviceType: serviceType?.toLowerCase(), // Ensure lowercase for backend
        description: description?.trim(),
        vehicleDetails: vehicleDetails?.trim(),
        images: images.map((image) => image.uri),
        location: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        },
      };

      let result;
      try {
        result = await dispatch(createServiceRequest({ requestData, token }));
      } catch (dispatchError) {
        console.error("🚛 Driver App: Redux dispatch error:", dispatchError);
        throw dispatchError;
      }

      if (createServiceRequest.fulfilled.match(result)) {
        setStatusText("Request Sent!");

        // Join service room for real-time updates
        if (result.payload?.serviceRequest?.id) {
          socketService.joinServiceRoom(result.payload.serviceRequest.id);
        }

        // Reset form
        dispatch(setWaitingForMechanic(true));

        setServiceType(null);
        setDescription("");
        setVehicleDetails("");
        setImages([]);
        actionSheetRef.current?.hide();

        setTimeout(() => {
          setStatusText("Submit");
        }, 2000);
      } else {
        setStatusText("Submit");
        const errorMessage =
          result.payload || "Failed to submit service request.";
        console.error("Service request failed:", errorMessage);

        // Provide specific error messages
        if (errorMessage.includes("Driver not found")) {
          Alert.alert(
            "Authentication Error",
            "Your driver profile was not found. Please log out and log in again, or contact support if the issue persists."
          );
        } else if (errorMessage.includes("Unauthorized")) {
          Alert.alert(
            "Authentication Error",
            "Your session has expired. Please log out and log in again."
          );
        } else {
          Alert.alert("Error", errorMessage);
        }
      }
    } catch (error) {
      console.error("Error submitting request:", error);
      setStatusText("Submit");
      Alert.alert(
        "Error",
        "Failed to submit service request. Please check your internet connection and try again."
      );
    }
  };

  if (!location?.coords) {
    return (
      <View style={styles.centeredContainer}>
        <Text>Loading location...</Text>
      </View>
    );
  }

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <EnhancedMapView
        style={styles.map}
        userLocation={location}
        mechanicLocation={mechanicLocation}
        activeServiceId={activeRequest?.id}
        showTraffic={true}
        showNearbyServices={true}
        onMapPress={() => {
          // Handle map press if needed
        }}
      />

      {/* Connection Status Indicator */}
      {/* <ConnectionStatusIndicator /> */}

      {/* Enhanced Connected State UI - Fixed at Bottom */}
      {activeRequest &&
      (activeRequest.status === "assigned" ||
        activeRequest.status === "accepted") ? (
        <View style={styles.bottomConnectedStateContainer}>
          {/* Compact Status Header */}
          <View style={styles.bottomStatusHeader}>
            <View style={styles.bottomStatusIconContainer}>
              <Text style={styles.bottomStatusIcon}>
                {mechanicLocation ? "🚗" : "⏳"}
              </Text>
            </View>
            <View style={styles.bottomStatusTextContainer}>
              <Text style={styles.bottomStatusTitle}>
                {mechanicLocation ? "Mechanic En Route" : "Mechanic Assigned"}
              </Text>
              <Text style={styles.bottomStatusSubtitle}>
                {mechanicLocation ? "Live tracking active" : "Waiting for location..."}
              </Text>
            </View>
            {mechanicLocation && (
              <View style={styles.bottomLiveIndicator}>
                <View style={styles.bottomLiveDot} />
                <Text style={styles.bottomLiveText}>LIVE</Text>
              </View>
            )}
          </View>

          {/* Compact Mechanic Info */}
          <View style={styles.bottomMechanicInfo}>
            <View style={styles.bottomMechanicDetails}>
              <View style={styles.bottomMechanicAvatar}>
                <Text style={styles.bottomMechanicAvatarText}>
                  {(activeRequest.mechanicName || "M").charAt(0).toUpperCase()}
                </Text>
              </View>
              <View style={styles.bottomMechanicTextInfo}>
                <Text style={styles.bottomMechanicName}>
                  {activeRequest.mechanicName || "Assigned Mechanic"}
                </Text>
                <Text style={styles.bottomServiceType}>
                  {activeRequest.serviceType || activeRequest.issue_type} Service
                </Text>
              </View>
            </View>
          </View>

          {/* Compact Action Buttons */}
          <View style={styles.bottomActionButtons}>
            <TouchableOpacity
              style={styles.bottomChatButton}
              onPress={() => {
                if (showChat) {
                  setChatMinimized(!chatMinimized);
                } else {
                  setShowChat(true);
                  setChatMinimized(false);
                }
              }}
            >
              <Text style={styles.bottomButtonIcon}>💬</Text>
              <Text style={styles.bottomButtonText}>Chat</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.bottomCallButton}
              onPress={() => {
                Alert.alert(
                  "Call Mechanic",
                  "Calling feature will be implemented soon."
                );
              }}
            >
              <Text style={styles.bottomButtonIcon}>📞</Text>
              <Text style={styles.bottomButtonText}>Call</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.bottomCancelButton,
                { opacity: isCancellingService ? 0.6 : 1 }
              ]}
              onPress={handleCancelService}
              disabled={isCancellingService}
            >
              <Text style={styles.bottomButtonIcon}>
                {isCancellingService ? "⏳" : "🚫"}
              </Text>
              <Text style={styles.bottomButtonText}>
                {isCancellingService ? "Cancelling..." : "Cancel"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        // Show service request buttons when no active request
        <View style={styles.buttonGroup}>
          {serviceButtons.map((label) => (
            <TouchableOpacity
              key={label}
              style={styles.actionButton}
              onPress={() => {
                console.log("🔧 Opening ActionSheet for service type:", label);
                setServiceType(label);
                actionSheetRef.current?.show();
              }}
            >
              <Text style={styles.buttonText}>{label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      <ActionSheet
        ref={actionSheetRef}
        closable={!isSubmitting}
        gestureEnabled={!isSubmitting}
        onOpen={() => console.log("🔧 ActionSheet opened")}
        onClose={() => console.log("🔧 ActionSheet closed")}
      >
        <View style={styles.sheetContent}>
          <Text style={styles.serviceTypeMainText}>
            You selected:{" "}
            <Text style={styles.serviceTypeBold}>
              {serviceType || "Service"}
            </Text>
          </Text>
          <Text style={styles.serviceTypeText}>
            Tell us more about your {serviceType || "service"} issue.
          </Text>
          <TextInput
            style={[
              styles.textInput,
              {
                borderColor: theme?.colors?.border || "#C6C6C8",
                color: theme?.colors?.text || "#000000",
                backgroundColor: theme?.colors?.surface || "#F2F2F7",
                textAlignVertical: "top", // Prevent layout issues
              },
            ]}
            placeholder="Describe your issue"
            placeholderTextColor={theme?.colors?.textTertiary || "#8E8E93"}
            returnKeyType="done"
            multiline={true}
            numberOfLines={3}
            value={description || ""}
            onChangeText={(text) => setDescription(text || "")}
            name="description"
          />
          <TextInput
            style={[
              styles.textInput,
              {
                borderColor: theme?.colors?.border || "#C6C6C8",
                color: theme?.colors?.text || "#000000",
                backgroundColor: theme?.colors?.surface || "#F2F2F7",
              },
            ]}
            placeholder="Enter Vehicle Number"
            placeholderTextColor={theme?.colors?.textTertiary || "#8E8E93"}
            returnKeyType="done"
            value={vehicleDetails || ""}
            onChangeText={(text) => setVehicleDetails(text || "")}
            name="vehicleDetails"
          />
          <Text
            style={[
              styles.serviceTypeText,
              { color: theme.colors.textPrimary },
            ]}
          >
            Attach up to 3 Images of your {serviceType || "service"} issue
          </Text>
          <View style={styles.imageButtonGroup}>
            <TouchableOpacity
              onPress={() => {
                try {
                  pickImage(setImages);
                } catch (error) {
                  console.error("Error picking image:", error);
                  Alert.alert(
                    "Error",
                    "Failed to pick image. Please try again."
                  );
                }
              }}
              style={styles.imageButton}
            >
              <Text style={styles.imageButtonText}>Pick Image</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                try {
                  takePhoto(setImages);
                } catch (error) {
                  console.error("Error taking photo:", error);
                  Alert.alert(
                    "Error",
                    "Failed to take photo. Please try again."
                  );
                }
              }}
              style={styles.imageButton}
            >
              <Text style={styles.imageButtonText}>Take Photo</Text>
            </TouchableOpacity>
          </View>

          {images.length > 0 && (
            <>
              <View style={styles.previewContainer}>
                {images.map((image, index) => (
                  <Image
                    key={index}
                    source={{ uri: image.uri }}
                    style={styles.imagePreview}
                  />
                ))}
              </View>
            </>
          )}
          <View style={styles.processButtonGroup}>
            {!isSubmitting && (
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => {
                  try {
                    actionSheetRef.current?.hide();
                    setImages([]);
                    setServiceType(null);
                    setDescription("");
                    setVehicleDetails("");
                  } catch (error) {
                    console.error("Error closing modal:", error);
                  }
                }}
              >
                <Text style={styles.cancelButtonText}>Close</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[
                styles.submitButton,
                isSubmitting && { width: "100%" }, // Make full width when submitting
              ]}
              onPress={() => {
                handleSubmit();
              }}
              disabled={isSubmitting}
            >
              <View style={styles.submitContent}>
                {isSubmitting && (
                  <ActivityIndicator
                    size="small"
                    color="#fff"
                    style={{ marginRight: 10 }}
                  />
                )}
                <Text style={styles.submitButtonText}>{statusText}</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </ActionSheet>

      {/* Chat Overlay */}
      <ChatOverlay
        visible={showChat}
        onClose={() => setShowChat(false)}
        serviceId={activeRequest?.id}
        mechanicName={activeRequest?.mechanicName || "Mechanic"}
        isMinimized={chatMinimized}
        onToggleMinimize={() => setChatMinimized(!chatMinimized)}
      />

      {/* Service Request Waiting UI - Only show if NOT connected to mechanic */}
      <ServiceRequestWaitingUI
        visible={
          (waitingForMechanic || minimumWaitTimeActive) &&
          !(activeRequest && (activeRequest.status === "assigned" || activeRequest.status === "accepted"))
        }
        serviceRequest={activeRequest}
        onCancel={handleCancelServiceRequest}
        onMechanicAccepted={handleMechanicAcceptance}
        onWaitTimeExpired={handleWaitTimeExpired}
        estimatedWaitTime={estimatedWaitTime}
      />

      {/* Debug Info - Remove in production */}
      {__DEV__ && (
        <View style={{ position: 'absolute', top: 100, right: 10, backgroundColor: 'rgba(0,0,0,0.7)', padding: 8, borderRadius: 4 }}>
          <Text style={{ color: 'white', fontSize: 10 }}>
            Status: {activeRequest?.status || 'none'}{'\n'}
            Waiting: {waitingForMechanic ? 'yes' : 'no'}{'\n'}
            MinWait: {minimumWaitTimeActive ? 'yes' : 'no'}{'\n'}
            ShowWait: {((waitingForMechanic || minimumWaitTimeActive) && !(activeRequest && (activeRequest.status === "assigned" || activeRequest.status === "accepted"))) ? 'yes' : 'no'}
          </Text>
        </View>
      )}
    </View>
  );
}

const createStyles = (theme) =>
  StyleSheet.create({
    container: { flex: 1 },
    centeredContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    actionButton: {
      backgroundColor: theme.colors.surface,
      paddingVertical: 13,
      paddingHorizontal: 15,
      width: safeWidth / 3 - 20,
      borderRadius: 25,
      ...theme.shadows.md,
      alignItems: "center",
      justifyContent: "center",
    },
    buttonText: {
      fontSize: 16,
      fontWeight: "bold",
      color: theme.colors.text,
    },
    map: { flex: 1 },
    buttonGroup: {
      position: "absolute",
      top: 70, // Increased from 60 to avoid status bar overlap
      flexDirection: "row",
      justifyContent: "space-around",
      width: "100%",
      paddingHorizontal: 15, // Increased padding for better spacing
      zIndex: 10,
    },
    processButtonGroup: {
      flexDirection: "row",
      justifyContent: "center",
      width: "100%",
      gap: 10,
    },
    cancelButton: {
      backgroundColor: theme.colors.surface,
      paddingVertical: 13,
      paddingHorizontal: 15,
      width: "48%",
      borderRadius: 25,
      ...theme.shadows.md,
      alignItems: "center",
      justifyContent: "center",
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    submitButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: 13,
      paddingHorizontal: 15,
      borderRadius: 25,
      width: "48%", // Default, will be overridden to 100% when isSubmitting
      ...theme.shadows.md,
      alignItems: "center",
      justifyContent: "center",
    },
    submitContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },

    cancelButtonText: {
      fontSize: 16,
      fontWeight: "bold",
      color: theme.colors.text,
    },
    submitButtonText: {
      fontSize: 16,
      fontWeight: "bold",
      color: theme.colors.white,
    },
    sheetContent: {
      padding: 20,
      alignItems: "center",
      minHeight: 400, // Ensure minimum height
      backgroundColor: theme.colors.background, // Ensure background is visible
    },
    serviceTypeMainText: {
      fontSize: 20,
      marginBottom: 20,
      textAlign: "center",
      color: theme.colors.textPrimary || "#000000", // Ensure text is visible
      fontWeight: "bold",
    },
    serviceTypeText: {
      fontSize: 15,
      marginBottom: 20,
      color: theme.colors.text || "#000000", // Ensure text is visible
      textAlign: "center",
    },
    serviceTypeBold: { fontWeight: "bold" },
    textInput: {
      width: "100%",
      minHeight: 50,
      maxHeight: 120,
      borderWidth: 2, // Make border more visible
      borderColor: "#007AFF", // Default blue border for debugging
      borderRadius: 10,
      paddingHorizontal: 15,
      paddingVertical: 12,
      marginBottom: 20,
      fontSize: 16,
      backgroundColor: "#F2F2F7", // Default background for debugging
      color: "#000000", // Default text color
      // Prevent NaN values and layout issues
      textAlignVertical: "top",
    },
    imageButtonGroup: {
      flexDirection: "row",
      justifyContent: "center",
      width: "100%",
      marginBottom: 20,
      gap: 10,
    },
    imageButton: {
      flex: 1,
      backgroundColor: theme.colors.surface,
      padding: 10,
      borderRadius: 10,
      alignItems: "center",
      marginHorizontal: 5,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    imageButtonText: { fontSize: 16, color: theme.colors.text },
    previewContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "center",
      marginBottom: 20,
    },
    imagePreview: {
      width: 100,
      height: 100,
      margin: 5,
      borderRadius: 10,
    },
    // Connected State Styles
    connectedStateContainer: {
      position: "absolute",
      top: 70,
      left: 15,
      right: 15,
      zIndex: 10,
    },
    bottomConnectedStateContainer: {
      position: "absolute",
      bottom: 100,
      left: 20,
      right: 20,
      zIndex: 1000,
      backgroundColor: "rgba(255, 255, 255, 0.98)",
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      borderBottomRightRadius: 20,
      borderBottomLeftRadius: 20,
      paddingTop: 20,
      paddingBottom: 34, // Account for safe area
      paddingHorizontal: 16,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: -4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 10,
    },

    // Bottom UI Styles
    bottomStatusHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 16,
    },
    bottomStatusIconContainer: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: "rgba(76, 175, 80, 0.1)",
      justifyContent: "center",
      alignItems: "center",
      marginRight: 12,
    },
    bottomStatusIcon: {
      fontSize: 18,
    },
    bottomStatusTextContainer: {
      flex: 1,
    },
    bottomStatusTitle: {
      fontSize: 16,
      fontWeight: "bold",
      color: "#2E7D32",
      marginBottom: 2,
    },
    bottomStatusSubtitle: {
      fontSize: 12,
      color: "#666",
    },
    bottomLiveIndicator: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: "rgba(76, 175, 80, 0.1)",
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    bottomLiveDot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: "#4CAF50",
      marginRight: 4,
    },
    bottomLiveText: {
      fontSize: 10,
      fontWeight: "600",
      color: "#4CAF50",
    },
    bottomMechanicInfo: {
      marginBottom: 16,
    },
    bottomMechanicDetails: {
      flexDirection: "row",
      alignItems: "center",
    },
    bottomMechanicAvatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: "#007AFF",
      justifyContent: "center",
      alignItems: "center",
      marginRight: 12,
    },
    bottomMechanicAvatarText: {
      color: "white",
      fontSize: 16,
      fontWeight: "bold",
    },
    bottomMechanicTextInfo: {
      flex: 1,
    },
    bottomMechanicName: {
      fontSize: 16,
      fontWeight: "bold",
      color: "#000",
      marginBottom: 2,
    },
    bottomServiceType: {
      fontSize: 14,
      color: "#666",
    },
    bottomActionButtons: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: 12,
    },
    bottomChatButton: {
      flex: 1,
      backgroundColor: "#007AFF",
      paddingVertical: 12,
      borderRadius: 12,
      alignItems: "center",
      justifyContent: "center",
    },
    bottomCallButton: {
      flex: 1,
      backgroundColor: "#4CAF50",
      paddingVertical: 12,
      borderRadius: 12,
      alignItems: "center",
      justifyContent: "center",
    },
    bottomCancelButton: {
      flex: 1,
      backgroundColor: "#FF5722",
      paddingVertical: 12,
      borderRadius: 12,
      alignItems: "center",
      justifyContent: "center",
    },
    bottomButtonIcon: {
      fontSize: 16,
      marginBottom: 2,
    },
    bottomButtonText: {
      fontSize: 12,
      fontWeight: "600",
      color: "white",
    },
    statusBanner: {
      paddingVertical: 8,
      paddingHorizontal: 15,
      borderRadius: 8,
      marginBottom: 10,
      alignItems: "center",
    },
    statusBannerText: {
      color: "white",
      fontSize: 14,
      fontWeight: "bold",
    },
    mechanicInfoCard: {
      borderRadius: 15,
      padding: 15,
      ...theme.shadows.md,
    },
    mechanicInfoContent: {
      flexDirection: "column",
    },
    mechanicDetails: {
      marginBottom: 15,
    },
    mechanicName: {
      fontSize: 18,
      fontWeight: "bold",
      marginBottom: 4,
    },
    serviceTypeText: {
      fontSize: 14,
      marginBottom: 4,
    },
    locationStatus: {
      fontSize: 12,
      fontStyle: "italic",
      marginBottom: 4,
    },
    serviceId: {
      fontSize: 10,
      fontStyle: "italic",
    },
    assignedTime: {
      fontSize: 10,
      fontStyle: "italic",
      marginTop: 2,
    },
    actionButtons: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: 10,
    },
    chatButton: {
      flex: 1,
      paddingVertical: 10,
      paddingHorizontal: 15,
      borderRadius: 20,
      alignItems: "center",
    },
    chatButtonText: {
      fontSize: 14,
      fontWeight: "bold",
    },
    callButton: {
      flex: 1,
      paddingVertical: 10,
      paddingHorizontal: 15,
      borderRadius: 20,
      alignItems: "center",
    },
    callButtonText: {
      fontSize: 14,
      fontWeight: "bold",
    },
    cancelButton: {
      flex: 1,
      paddingVertical: 10,
      paddingHorizontal: 15,
      borderRadius: 20,
      alignItems: "center",
      marginLeft: 8,
    },
    cancelButtonText: {
      fontSize: 14,
      fontWeight: "bold",
    },

    // Enhanced Connected State Styles
    enhancedStatusBanner: {
      paddingVertical: 12,
      paddingHorizontal: 20,
      borderRadius: 12,
      marginBottom: 15,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    statusBannerContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    statusIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: "rgba(255, 255, 255, 0.2)",
      justifyContent: "center",
      alignItems: "center",
    },
    statusIcon: {
      fontSize: 20,
    },
    statusTextContainer: {
      flex: 1,
      marginLeft: 12,
    },
    statusBannerTitle: {
      color: "white",
      fontSize: 16,
      fontWeight: "bold",
      marginBottom: 2,
    },
    statusBannerSubtitle: {
      color: "rgba(255, 255, 255, 0.8)",
      fontSize: 12,
    },
    pulseIndicator: {
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: "rgba(255, 255, 255, 0.3)",
      justifyContent: "center",
      alignItems: "center",
    },
    pulseDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
    },
    enhancedMechanicCard: {
      borderRadius: 20,
      padding: 20,
      marginBottom: 15,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 6,
      elevation: 8,
    },
    mechanicCardHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 15,
    },
    mechanicAvatarContainer: {
      position: "relative",
    },
    mechanicAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 15,
    },
    mechanicAvatarText: {
      color: "white",
      fontSize: 20,
      fontWeight: "bold",
    },
    onlineIndicator: {
      position: "absolute",
      bottom: 2,
      right: 12,
      width: 14,
      height: 14,
      borderRadius: 7,
      borderWidth: 2,
      borderColor: "white",
    },
    mechanicInfoSection: {
      flex: 1,
    },
    locationStatusContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 8,
    },
    locationStatusDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginRight: 8,
    },
    locationStatusText: {
      fontSize: 12,
      fontWeight: "500",
    },
    serviceDetailsSection: {
      marginBottom: 20,
    },
    serviceDetailRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: "rgba(0, 0, 0, 0.05)",
    },
    serviceDetailLabel: {
      fontSize: 14,
      fontWeight: "500",
    },
    serviceDetailValue: {
      fontSize: 14,
      fontWeight: "600",
    },
    enhancedActionButtons: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: 12,
    },
    enhancedChatButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 25,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 3,
      elevation: 3,
    },
    enhancedCallButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 25,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 3,
      elevation: 3,
    },
    enhancedCancelButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 25,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 3,
      elevation: 3,
    },
    buttonIconContainer: {
      marginRight: 6,
    },
    buttonIcon: {
      fontSize: 16,
    },
    enhancedButtonText: {
      fontSize: 14,
      fontWeight: "600",
    },
  });
