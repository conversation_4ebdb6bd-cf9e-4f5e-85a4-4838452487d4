/**
 * Unified API Services
 * This file contains all API service methods organized by feature
 * Uses the configured axios instance with automatic token handling
 */

import api from "./api";

// ===== AUTHENTICATION SERVICES =====
export const authService = {
  // Login method for drivers only
  loginDriver: (email, password) => {
    return api.post("/drivers/login", { email, password });
  },

  // Logout
  logout: () => api.post("/auth/logout"),

  // Password reset
  requestPasswordReset: (email) => api.post("/auth/password-reset", { email }),
  resetPassword: (token, newPassword) =>
    api.post("/auth/password-reset/confirm", { token, newPassword }),
};

// ===== DRIVER SERVICES =====
export const driverService = {
  // Profile management
  getProfile: () => api.get("/drivers/profile"),
  updateProfile: (data) => api.put("/drivers/profile", data),
  uploadProfileImage: (imageData) => {
    const formData = new FormData();
    formData.append("profileImage", {
      uri: imageData.uri,
      type: imageData.type || "image/jpeg",
      name: imageData.fileName || "profile.jpg",
    });
    return api.post("/drivers/upload-profile-image", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  },

  // Status management
  updateStatus: (isActive) =>
    api.patch("/drivers/status", { is_active: isActive }),
  updateLocation: (location) => api.patch("/drivers/location", location),

  // Statistics
  getStats: () => api.get("/drivers/stats"),

  // Service requests
  requestService: (data) => api.post("/drivers/request-mechanic", data),
  getServiceHistory: () => api.get("/drivers/service-history"),
  getServiceDetails: (serviceId) => api.get(`/drivers/service/${serviceId}`),
  updateServiceStatus: (serviceId, status) => {
    console.log(`🔗 Driver API: Calling PUT /requests/${serviceId}/status with status:`, status);
    return api.put(`/requests/${serviceId}/status`, { status });
  },
  rateService: (serviceId, rating, review) =>
    api.post(`/drivers/service/${serviceId}/rate`, { rating, review }),

  // FCM token management
  updateFCMToken: (token) =>
    api.put("/drivers/fcm-token", { fcm_token: token }),
};

// ===== SHARED SERVICES =====
export const sharedService = {
  // File uploads
  uploadFile: (file, type = "general") => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("type", type);
    return api.post("/files/upload", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
  },

  // Location services
  updateLocation: (location) => api.post("/location/update", location),
  getNearbyServices: (location, radius = 10) =>
    api.get(
      `/location/nearby?lat=${location.latitude}&lng=${location.longitude}&radius=${radius}`
    ),

  // Chat/messaging
  sendMessage: (recipientId, message) =>
    api.post("/chat/send", { recipientId, message }),
  getMessages: (conversationId) =>
    api.get(`/chat/conversation/${conversationId}`),
  markMessagesRead: (conversationId) =>
    api.patch(`/chat/conversation/${conversationId}/read`),
};

// Export all services as a single object for easy importing
export default {
  auth: authService,
  driver: driverService,
  shared: sharedService,
};
