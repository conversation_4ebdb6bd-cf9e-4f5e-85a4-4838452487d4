import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Animated,
  Dimensions,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useSelector } from 'react-redux';
import { selectUser } from '../store/slices/authSlice';
import socketService from '../services/socketService';

const { height: screenHeight } = Dimensions.get('window');

const ChatOverlay = ({ 
  visible, 
  onClose, 
  serviceId, 
  mechanicName = 'Mechanic',
  isMinimized = false,
  onToggleMinimize 
}) => {
  const { theme } = useTheme();
  const user = useSelector(selectUser);
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [unreadCount, setUnreadCount] = useState(0);
  const flatListRef = useRef(null);
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;

  useEffect(() => {
    if (visible) {
      // Animate in
      const targetValue = isMinimized ? screenHeight - 60 : screenHeight * 0.4;
      console.log(`💬 ChatOverlay: Animating to ${targetValue}, isMinimized: ${isMinimized}`);

      Animated.spring(slideAnim, {
        toValue: targetValue,
        useNativeDriver: false,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      // Animate out
      console.log('💬 ChatOverlay: Animating out');
      Animated.spring(slideAnim, {
        toValue: screenHeight,
        useNativeDriver: false,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [visible, isMinimized]);

  useEffect(() => {
    if (!serviceId) return;

    // Set up message listener
    const handleNewMessage = (data) => {
      console.log('💬 Driver ChatOverlay: Received message:', data);
      if (data.serviceId === serviceId) {
        console.log('💬 Driver ChatOverlay: Message is for this service, adding to messages');
        const newMessage = {
          id: Date.now().toString(),
          text: data.message,
          sender: data.senderType === 'driver' ? 'driver' : 'mechanic',
          senderName: data.senderName,
          timestamp: new Date(data.timestamp),
          type: data.messageType || 'text',
        };

        setMessages(prev => [...prev, newMessage]);

        // Increment unread count if minimized and message is from mechanic
        if (isMinimized && data.senderType === 'mechanic') {
          setUnreadCount(prev => prev + 1);
        }

        // Auto-scroll to bottom
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    };

    socketService.addEventListener('new_message', handleNewMessage);

    return () => {
      socketService.removeEventListener('new_message', handleNewMessage);
    };
  }, [serviceId, isMinimized]);

  const sendMessage = () => {
    if (!inputText.trim()) return;

    console.log(`💬 Driver ChatOverlay: Sending message to service ${serviceId}:`, inputText.trim());

    const newMessage = {
      id: Date.now().toString(),
      text: inputText.trim(),
      sender: 'driver',
      senderName: user?.name || 'You',
      timestamp: new Date(),
      type: 'text',
    };

    // Add to local messages
    setMessages(prev => [...prev, newMessage]);

    // Send via Socket.IO
    socketService.sendMessage(serviceId, inputText.trim());

    // Clear input
    setInputText('');

    // Auto-scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const handleToggleMinimize = () => {
    console.log(`💬 ChatOverlay: Toggle minimize called, current state: ${isMinimized}`);
    if (isMinimized) {
      setUnreadCount(0); // Clear unread count when expanding
    }
    onToggleMinimize();
  };

  const formatTime = (timestamp) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessage = ({ item }) => {
    const isOwnMessage = item.sender === 'driver';
    
    return (
      <View style={[
        styles.messageContainer,
        isOwnMessage ? styles.ownMessage : styles.otherMessage
      ]}>
        <View style={[
          styles.messageBubble,
          {
            backgroundColor: isOwnMessage ? theme.colors.primary : theme.colors.surface,
          }
        ]}>
          <Text style={[
            styles.messageText,
            { color: isOwnMessage ? 'white' : theme.colors.textPrimary }
          ]}>
            {item.text}
          </Text>
          <Text style={[
            styles.messageTime,
            { color: isOwnMessage ? 'rgba(255,255,255,0.7)' : theme.colors.textSecondary }
          ]}>
            {formatTime(item.timestamp)}
          </Text>
        </View>
      </View>
    );
  };

  if (!visible) return null;

  return (
    <Animated.View 
      style={[
        styles.overlay,
        {
          backgroundColor: theme.colors.background,
          borderColor: theme.colors.border,
          transform: [{ translateY: slideAnim }],
          height: isMinimized ? 60 : screenHeight * 0.6,
        }
      ]}
    >
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity onPress={handleToggleMinimize} style={styles.headerLeft}>
          <Ionicons 
            name={isMinimized ? "chevron-up" : "chevron-down"} 
            size={24} 
            color={theme.colors.textPrimary} 
          />
          <Text style={[styles.headerTitle, { color: theme.colors.textPrimary }]}>
            Chat with {mechanicName}
          </Text>
          {isMinimized && unreadCount > 0 && (
            <View style={[styles.unreadBadge, { backgroundColor: theme.colors.primary }]}>
              <Text style={styles.unreadText}>{unreadCount}</Text>
            </View>
          )}
        </TouchableOpacity>
        
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color={theme.colors.textPrimary} />
        </TouchableOpacity>
      </View>

      {/* Chat Content - Only show when not minimized */}
      {!isMinimized && (
        <>
          {/* Messages */}
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item) => item.id}
            style={styles.messagesList}
            contentContainerStyle={styles.messagesContent}
            showsVerticalScrollIndicator={false}
          />

          {/* Input */}
          <KeyboardAvoidingView 
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.inputContainer}
          >
            <View style={[styles.inputRow, { backgroundColor: theme.colors.surface }]}>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    backgroundColor: theme.colors.background,
                    color: theme.colors.textPrimary,
                    borderColor: theme.colors.border,
                  }
                ]}
                placeholder="Type a message..."
                placeholderTextColor={theme.colors.textSecondary}
                value={inputText}
                onChangeText={setInputText}
                multiline
                maxLength={500}
              />
              <TouchableOpacity
                style={[
                  styles.sendButton,
                  {
                    backgroundColor: inputText.trim() ? theme.colors.primary : theme.colors.textSecondary,
                  }
                ]}
                onPress={sendMessage}
                disabled={!inputText.trim()}
              >
                <Ionicons name="send" size={20} color="white" />
              </TouchableOpacity>
            </View>
          </KeyboardAvoidingView>
        </>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderTopWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  unreadBadge: {
    marginLeft: 8,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  unreadText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  messageContainer: {
    marginVertical: 4,
  },
  ownMessage: {
    alignItems: 'flex-end',
  },
  otherMessage: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  messageTime: {
    fontSize: 12,
    marginTop: 4,
  },
  inputContainer: {
    paddingBottom: Platform.OS === 'ios' ? 20 : 0,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default ChatOverlay;
