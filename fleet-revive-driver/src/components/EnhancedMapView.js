import React, { useState, useEffect, useRef, useMemo, useCallback } from "react";
import { StyleSheet, View, Alert, TouchableOpacity, Animated, ActivityIndicator } from "react-native";
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "react-native-maps";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import CustomText from "./CustomText";
import locationService from "../services/locationService";
import directionsService from "../services/directionsService";
import { ROUTE_CONFIG } from "../config/constants";

const EnhancedMapView = ({
  userLocation,
  serviceLocations = [],
  mechanicLocation = null,
  activeServiceId = null,
  showTraffic = true,
  showNearbyServices = true,
  onMapPress,
  style,
}) => {
  const { theme } = useTheme();
  const mapRef = useRef(null);
  const [mapType, setMapType] = useState("standard");
  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [routeSteps, setRouteSteps] = useState([]);
  const [nearbyMechanics, setNearbyMechanics] = useState([]);
  const [estimatedTime, setEstimatedTime] = useState(null);
  const [showUserLocation, setShowUserLocation] = useState(true);
  const [isLoadingRoute, setIsLoadingRoute] = useState(false);
  const [routeError, setRouteError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  // Animation for mechanic marker pulsing
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  // Animation for live indicator pulsing
  const liveIndicatorAnimation = useRef(new Animated.Value(1)).current;

  // Memoized calculations for performance
  const initialRegion = useMemo(() => ({
    latitude: userLocation.coords.latitude,
    longitude: userLocation.coords.longitude,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  }), [userLocation.coords.latitude, userLocation.coords.longitude]);

  const userLocationCircle = useMemo(() => ({
    latitude: userLocation.coords.latitude,
    longitude: userLocation.coords.longitude,
  }), [userLocation.coords.latitude, userLocation.coords.longitude]);

  const mechanicMarkerCoordinate = useMemo(() => {
    if (!mechanicLocation) return null;
    return {
      latitude: mechanicLocation.latitude,
      longitude: mechanicLocation.longitude,
    };
  }, [mechanicLocation?.latitude, mechanicLocation?.longitude]);

  // Note: Mock data removed - nearby mechanics should come from real API data
  // TODO: Implement real API call to fetch nearby mechanics

  // Pulsing animation for mechanic marker
  useEffect(() => {
    if (mechanicLocation) {
      const startPulseAnimation = () => {
        Animated.loop(
          Animated.sequence([
            Animated.timing(pulseAnimation, {
              toValue: 1.3,
              duration: 1000,
              useNativeDriver: true,
            }),
            Animated.timing(pulseAnimation, {
              toValue: 1,
              duration: 1000,
              useNativeDriver: true,
            }),
          ])
        ).start();
      };
      startPulseAnimation();
    } else {
      pulseAnimation.setValue(1);
    }
  }, [mechanicLocation, pulseAnimation]);

  // Live indicator pulsing animation
  useEffect(() => {
    if (routeCoordinates.length > 0) {
      const startLiveAnimation = () => {
        Animated.loop(
          Animated.sequence([
            Animated.timing(liveIndicatorAnimation, {
              toValue: 1.5,
              duration: 800,
              useNativeDriver: true,
            }),
            Animated.timing(liveIndicatorAnimation, {
              toValue: 1,
              duration: 800,
              useNativeDriver: true,
            }),
          ])
        ).start();
      };
      startLiveAnimation();
    } else {
      liveIndicatorAnimation.setValue(1);
    }
  }, [routeCoordinates.length, liveIndicatorAnimation]);

  useEffect(() => {
    if (showNearbyServices) {
      // TODO: Replace with real API call to fetch nearby mechanics
      // For now, set empty array to remove mock data
      setNearbyMechanics([]);
    }
  }, [showNearbyServices, userLocation]);

  useEffect(() => {
    if (__DEV__) {
      console.log('🗺️ Driver App: Route calculation trigger:', {
        hasMechanicLocation: !!mechanicLocation,
        hasUserLocation: !!userLocation,
        mechanicLat: mechanicLocation?.latitude,
        mechanicLng: mechanicLocation?.longitude,
        userLat: userLocation?.coords?.latitude,
        userLng: userLocation?.coords?.longitude,
      });
    }

    // Calculate route if mechanic location is available
    if (mechanicLocation && userLocation && mechanicLocation.latitude && mechanicLocation.longitude) {
      console.log('🗺️ Driver App: Starting route calculation...');
      calculateRoute();
    } else {
      console.log('🗺️ Driver App: Missing location data for route calculation');
      setRouteCoordinates([]);
      setRouteSteps([]);
      setEstimatedTime(null);
    }
  }, [mechanicLocation, userLocation]);

  // Dynamic camera adjustment for real-time updates
  useEffect(() => {
    if (mechanicLocation && userLocation && mapRef.current) {
      const centerLat = (mechanicLocation.latitude + userLocation.coords.latitude) / 2;
      const centerLng = (mechanicLocation.longitude + userLocation.coords.longitude) / 2;

      // Calculate appropriate zoom level based on distance
      const latDelta = Math.abs(mechanicLocation.latitude - userLocation.coords.latitude);
      const lngDelta = Math.abs(mechanicLocation.longitude - userLocation.coords.longitude);
      const maxDelta = Math.max(latDelta, lngDelta);
      const zoomLevel = Math.max(0.01, maxDelta * 1.5); // Add padding

      // Smooth camera animation to center both locations
      mapRef.current.animateToRegion({
        latitude: centerLat,
        longitude: centerLng,
        latitudeDelta: zoomLevel,
        longitudeDelta: zoomLevel,
      }, 1000);
    }
  }, [mechanicLocation?.latitude, mechanicLocation?.longitude]);

  const calculateRoute = async () => {
    if (!mechanicLocation || !userLocation) {
      console.log('🗺️ Driver App: Cannot calculate route - missing location data');
      return;
    }

    console.log('🗺️ Driver App: Calculating route between driver and mechanic...');
    setIsLoadingRoute(true);
    setRouteError(null);
    try {
      const origin = {
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude,
      };
      const destination = {
        latitude: mechanicLocation.latitude,
        longitude: mechanicLocation.longitude,
      };

      // Get detailed route with turn-by-turn directions
      const routeData = await directionsService.getRoute(origin, destination, {
        ...ROUTE_CONFIG,
        avoidTolls: false, // Allow tolls for faster routes
        alternatives: false, // Get single best route
      });

      // Set route coordinates from Google Directions API
      setRouteCoordinates(routeData.coordinates);
      setRouteSteps(routeData.steps);

      // Set estimated time from API response
      const durationMinutes = Math.round(routeData.summary.duration.value / 60);
      setEstimatedTime(durationMinutes);

      console.log(`🚗 Driver route calculated: ${(routeData.summary.distance.value / 1000).toFixed(1)}km, ${durationMinutes}min`);
      console.log(`🛣️ Route has ${routeData.steps.length} turn-by-turn steps`);

      // Smooth camera transition to show both locations and route
      if (mapRef.current && routeData.coordinates.length > 0) {
        const allCoordinates = [
          { latitude: userLocation.coords.latitude, longitude: userLocation.coords.longitude },
          { latitude: mechanicLocation.latitude, longitude: mechanicLocation.longitude },
          ...routeData.coordinates
        ];

        // Use smooth animation with better padding for enhanced UI
        setTimeout(() => {
          mapRef.current.fitToCoordinates(allCoordinates, {
            edgePadding: { top: 150, right: 50, bottom: 200, left: 50 },
            animated: true,
          });
        }, 500); // Small delay to ensure markers are rendered
      }

    } catch (error) {
      console.error("Error calculating route:", error);
      setRouteError(error.message || "Failed to calculate route");

      // Retry logic for transient failures
      if (retryCount < 2) {
        console.log(`🔄 Driver App: Retrying route calculation (attempt ${retryCount + 1})`);
        setRetryCount(prev => prev + 1);
        setTimeout(() => {
          calculateRoute();
        }, 2000 * (retryCount + 1)); // Exponential backoff
        return;
      }

      // Fallback to simple route if API fails after retries
      const startLat = userLocation.coords.latitude;
      const startLng = userLocation.coords.longitude;
      const endLat = mechanicLocation.latitude;
      const endLng = mechanicLocation.longitude;

      const route = [
        { latitude: startLat, longitude: startLng },
        {
          latitude: startLat + (endLat - startLat) * 0.3,
          longitude: startLng + (endLng - startLng) * 0.3
        },
        {
          latitude: startLat + (endLat - startLat) * 0.7,
          longitude: startLng + (endLng - startLng) * 0.7
        },
        { latitude: endLat, longitude: endLng },
      ];

      setRouteCoordinates(route);

      // Calculate estimated time using fallback method
      try {
        const travelInfo = await locationService.getEstimatedTravelTime(
          startLat, startLng, endLat, endLng
        );
        setEstimatedTime(travelInfo);
      } catch (fallbackError) {
        console.error("Fallback time calculation failed:", fallbackError);
        setEstimatedTime(15); // Default 15 minutes
      }

      console.log("🚗 Using fallback route calculation");
    } finally {
      setIsLoadingRoute(false);
    }
  };

  const centerOnUser = () => {
    if (userLocation && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }, 1000);
    }
  };

  const centerOnMechanic = () => {
    if (mechanicLocation && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: mechanicLocation.latitude,
        longitude: mechanicLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }, 1000);
    }
  };

  const fitToRoute = () => {
    if (routeCoordinates.length > 0 && mapRef.current) {
      mapRef.current.fitToCoordinates(routeCoordinates, {
        edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
        animated: true,
      });
    }
  };

  const toggleMapType = () => {
    const types = ["standard", "satellite", "hybrid"];
    const currentIndex = types.indexOf(mapType);
    const nextIndex = (currentIndex + 1) % types.length;
    setMapType(types[nextIndex]);
  };

  const renderMechanicMarker = (mechanic) => (
    <Marker
      key={mechanic.id}
      coordinate={{
        latitude: mechanic.latitude,
        longitude: mechanic.longitude,
      }}
      title={mechanic.name}
      description={`Rating: ${mechanic.rating} • ${mechanic.distance} • ${mechanic.services.join(", ")}`}
      pinColor={mechanic.available ? "#4CAF50" : "#F44336"}
    >
      <View style={[
        styles.mechanicMarker,
        { backgroundColor: mechanic.available ? "#4CAF50" : "#F44336" }
      ]}>
        <Ionicons 
          name="build" 
          size={20} 
          color="white" 
        />
      </View>
    </Marker>
  );

  const renderServiceLocationMarker = (service) => (
    <Marker
      key={service.id}
      coordinate={{
        latitude: service.latitude,
        longitude: service.longitude,
      }}
      title={`${service.serviceType} Service`}
      description={service.description}
      pinColor={service.id === activeServiceId ? "#FF9800" : "#2196F3"}
    >
      <View style={[
        styles.serviceMarker,
        { backgroundColor: service.id === activeServiceId ? "#FF9800" : "#2196F3" }
      ]}>
        <Ionicons 
          name={service.serviceType === "Tire" ? "car-sport" : 
                service.serviceType === "Engine" ? "build" : "car"} 
          size={20} 
          color="white" 
        />
      </View>
    </Marker>
  );

  if (!userLocation) {
    return (
      <View style={[styles.container, styles.centered]}>
        <CustomText>Loading map...</CustomText>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <MapView
        ref={mapRef}
        style={styles.map}
        mapType={mapType}
        initialRegion={initialRegion}
        showsUserLocation={showUserLocation}
        showsMyLocationButton={false}
        showsCompass={true}
        showsTraffic={showTraffic}
        showsBuildings={true}
        showsIndoors={true}
        showsPointsOfInterest={true}
        onPress={onMapPress}
        customMapStyle={theme.mapStyle}
        maxZoomLevel={18}
        minZoomLevel={8}
        rotateEnabled={true}
        scrollEnabled={true}
        zoomEnabled={true}
        pitchEnabled={false}
        loadingEnabled={true}
        loadingIndicatorColor={theme.colors.primary}
        loadingBackgroundColor={theme.colors.background}
      >
        {/* User location circle */}
        {userLocation && (
          <Circle
            center={userLocationCircle}
            radius={100}
            fillColor="rgba(66, 165, 245, 0.2)"
            strokeColor="rgba(66, 165, 245, 0.8)"
            strokeWidth={2}
          />
        )}

        {/* Enhanced Mechanic location marker with animation */}
        {mechanicMarkerCoordinate && (
          <Marker
            coordinate={mechanicMarkerCoordinate}
            title="Your Mechanic"
            description="Real-time location"
            anchor={{ x: 0.5, y: 0.5 }}
            flat={true}
            rotation={mechanicLocation?.heading || 0}
            tracksViewChanges={false}
          >
            <View style={styles.enhancedMechanicMarker}>
              {/* Pulsing background circle */}
              <Animated.View style={[
                styles.mechanicPulseOuter,
                { transform: [{ scale: pulseAnimation }] }
              ]}>
                <View style={styles.mechanicPulseInner} />
              </Animated.View>

              {/* Main mechanic marker */}
              <View style={[styles.mechanicMarkerContainer, { backgroundColor: "#FF5722" }]}>
                <Ionicons name="car" size={24} color="white" />
              </View>

              {/* Direction indicator */}
              {mechanicLocation.heading && (
                <View style={[
                  styles.directionIndicator,
                  { transform: [{ rotate: `${mechanicLocation.heading}deg` }] }
                ]}>
                  <View style={styles.directionArrow} />
                </View>
              )}
            </View>
          </Marker>
        )}

        {/* Enhanced Route polyline with gradient effect */}
        {routeCoordinates.length > 0 && (
          <>
            {/* Background route line (shadow effect) */}
            <Polyline
              coordinates={routeCoordinates}
              strokeColor="rgba(0, 0, 0, 0.2)"
              strokeWidth={8}
              lineDashPattern={[]}
              zIndex={1}
            />

            {/* Main route line */}
            <Polyline
              coordinates={routeCoordinates}
              strokeColor={theme.colors.primary}
              strokeWidth={5}
              lineDashPattern={[]}
              zIndex={2}
            />

            {/* Animated progress line */}
            <Polyline
              coordinates={routeCoordinates}
              strokeColor="#4CAF50"
              strokeWidth={3}
              lineDashPattern={[10, 10]}
              zIndex={3}
            />
          </>
        )}

        {/* Nearby mechanics */}
        {showNearbyServices && nearbyMechanics.map(renderMechanicMarker)}

        {/* Service locations */}
        {serviceLocations.map(renderServiceLocationMarker)}
      </MapView>

      {/* Enhanced Route information overlay */}
      {mechanicLocation && (
        <View style={styles.enhancedRouteInfo}>
          <View style={styles.routeInfoHeader}>
            <View style={styles.routeStatusIndicator}>
              {isLoadingRoute ? (
                <ActivityIndicator size="small" color="#4CAF50" />
              ) : (
                <Ionicons
                  name={routeCoordinates.length > 0 ? "navigation" : "location"}
                  size={20}
                  color="#4CAF50"
                />
              )}
            </View>
            <View style={styles.routeInfoTextContainer}>
              <CustomText style={styles.routeInfoTitle}>
                Mechanic En Route
              </CustomText>
              {routeCoordinates.length > 0 && estimatedTime ? (
                <View style={styles.routeDetailsContainer}>
                  <CustomText style={styles.routeInfoTime}>
                    ETA: {typeof estimatedTime === 'object' ? estimatedTime.text : `${estimatedTime} min`}
                  </CustomText>
                  {typeof estimatedTime === 'object' && estimatedTime.distance && (
                    <CustomText style={styles.routeInfoDistance}>
                      Distance: {estimatedTime.distance}
                    </CustomText>
                  )}
                </View>
              ) : (
                <View style={styles.loadingContainer}>
                  {routeError ? (
                    <View style={styles.errorContainer}>
                      <CustomText style={styles.routeInfoError}>
                        Route calculation failed
                      </CustomText>
                      <TouchableOpacity
                        style={styles.retryButton}
                        onPress={() => {
                          setRetryCount(0);
                          setRouteError(null);
                          calculateRoute();
                        }}
                      >
                        <Ionicons name="refresh" size={14} color="#4CAF50" />
                        <CustomText style={styles.retryButtonText}>Retry</CustomText>
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <>
                      <CustomText style={styles.routeInfoCalculating}>
                        {isLoadingRoute ? "Calculating optimal route..." : "Waiting for mechanic location..."}
                      </CustomText>
                      {isLoadingRoute && (
                        <View style={styles.loadingDots}>
                          <View style={[styles.loadingDot, styles.loadingDot1]} />
                          <View style={[styles.loadingDot, styles.loadingDot2]} />
                          <View style={[styles.loadingDot, styles.loadingDot3]} />
                        </View>
                      )}
                    </>
                  )}
                </View>
              )}
            </View>
          </View>

          {/* Live tracking indicator */}
          <View style={styles.liveTrackingIndicator}>
            <Animated.View style={[
              styles.liveIndicatorDot,
              {
                backgroundColor: routeCoordinates.length > 0 ? "#4CAF50" : "#FF9800",
                transform: [{ scale: routeCoordinates.length > 0 ? liveIndicatorAnimation : 1 }]
              }
            ]} />
            <CustomText style={[
              styles.liveTrackingText,
              { color: routeCoordinates.length > 0 ? "#4CAF50" : "#FF9800" }
            ]}>
              {routeCoordinates.length > 0 ? "Live" : "Connecting"}
            </CustomText>
          </View>
        </View>
      )}

      {/* Map controls */}
      <View style={styles.mapControls}>
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme.colors.surface }]}
          onPress={centerOnUser}
        >
          <Ionicons name="locate" size={24} color={theme.colors.primary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme.colors.surface }]}
          onPress={toggleMapType}
        >
          <Ionicons name="layers" size={24} color={theme.colors.primary} />
        </TouchableOpacity>

        {mechanicLocation && (
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.surface }]}
            onPress={centerOnMechanic}
          >
            <Ionicons name="person" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
        )}

        {routeCoordinates.length > 0 && (
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.surface }]}
            onPress={fitToRoute}
          >
            <Ionicons name="resize" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Estimated time display */}
      {estimatedTime && (
        <View style={[styles.timeDisplay, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="time" size={16} color={theme.colors.primary} />
          <CustomText style={[styles.timeText, { color: theme.colors.textPrimary }]}>
            {estimatedTime.durationText} • {estimatedTime.distanceText}
          </CustomText>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: "absolute",
    right: 16,
    top: 60,
    gap: 8,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },

  // Enhanced Route Info Styles
  enhancedRouteInfo: {
    position: "absolute",
    top: 60,
    left: 16,
    right: 16,
    backgroundColor: "rgba(255, 255, 255, 0.98)",
    borderRadius: 16,
    padding: 16,
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    borderWidth: 1,
    borderColor: "rgba(76, 175, 80, 0.2)",
  },
  routeInfoHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  routeStatusIndicator: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: "rgba(76, 175, 80, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  routeInfoTextContainer: {
    flex: 1,
  },
  routeInfoTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E7D32",
    marginBottom: 4,
  },
  routeDetailsContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  routeInfoTime: {
    fontSize: 14,
    fontWeight: "600",
    color: "#1976D2",
  },
  routeInfoDistance: {
    fontSize: 14,
    color: "#666",
  },
  routeInfoCalculating: {
    fontSize: 14,
    color: "#666",
    fontStyle: "italic",
  },
  liveTrackingIndicator: {
    position: "absolute",
    top: 12,
    right: 12,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(76, 175, 80, 0.1)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  liveIndicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#4CAF50",
    marginRight: 4,
  },
  liveTrackingText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#4CAF50",
  },
  loadingContainer: {
    alignItems: "flex-start",
  },
  loadingDots: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: "#4CAF50",
    marginRight: 4,
  },
  loadingDot1: {
    opacity: 0.4,
  },
  loadingDot2: {
    opacity: 0.7,
  },
  loadingDot3: {
    opacity: 1,
  },
  errorContainer: {
    alignItems: "flex-start",
  },
  routeInfoError: {
    fontSize: 14,
    color: "#F44336",
    fontStyle: "italic",
    marginBottom: 8,
  },
  retryButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(76, 175, 80, 0.1)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "rgba(76, 175, 80, 0.3)",
  },
  retryButtonText: {
    fontSize: 12,
    color: "#4CAF50",
    fontWeight: "600",
    marginLeft: 4,
  },

  routeInfo: {
    position: "absolute",
    top: 60,
    left: 16,
    right: 80,
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderRadius: 12,
    padding: 12,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  routeInfoContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  routeInfoText: {
    fontSize: 14,
    fontWeight: "600",
  },
  routeInfoSeparator: {
    fontSize: 14,
    fontWeight: "400",
  },
  routeLoadingIndicator: {
    marginTop: 4,
  },
  routeLoadingText: {
    fontSize: 12,
    fontStyle: "italic",
  },
  // Enhanced Mechanic Marker Styles
  enhancedMechanicMarker: {
    width: 80,
    height: 80,
    justifyContent: "center",
    alignItems: "center",
  },
  mechanicPulseOuter: {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(255, 87, 34, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  mechanicPulseInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "rgba(255, 87, 34, 0.3)",
  },
  mechanicMarkerContainer: {
    position: "absolute",
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: "white",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  directionIndicator: {
    position: "absolute",
    top: -5,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  directionArrow: {
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderBottomWidth: 12,
    borderLeftColor: "transparent",
    borderRightColor: "transparent",
    borderBottomColor: "#FF5722",
  },
  mechanicMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "white",
  },
  serviceMarker: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "white",
  },
  timeDisplay: {
    position: "absolute",
    top: 16,
    left: 16,
    right: 16,
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  timeText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: "500",
  },
});

export default EnhancedMapView;
