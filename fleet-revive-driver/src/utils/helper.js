import * as Location from "expo-location";
import * as ImagePicker from "expo-image-picker";

export const getLocation = async () => {
  try {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      return null;
    }
    const location = await Location.getCurrentPositionAsync({});
    return location;
  } catch (error) {
    console.error("Error getting location:", error);
    return null;
  }
};

export const watchLocation = async (onUpdate) => {
  try {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      return null;
    }

    const subscription = await Location.watchPositionAsync(
      {
        accuracy: Location.Accuracy.High,
        timeInterval: 5000, // CRITICAL FIX: Increase to 5 seconds
        distanceInterval: 10, // CRITICAL FIX: Increase to 10 meters to prevent spam
      },
      onUpdate
    );
    console.log("Location watching started:", subscription);
    return subscription;
  } catch (error) {
    console.error("Error watching location:", error);
    return null;
  }
};

export const pickImage = async (setImages) => {
  try {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      alert("Permission to access media library is required!");
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setImages((prev) => [...prev, ...result.assets]);
    }
  } catch (error) {
    console.error("Error picking image:", error);
  }
};

export const takePhoto = async (setImages) => {
  try {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") {
      alert("Permission to access camera is required!");
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setImages((prev) => [...prev, ...result.assets]);
    }
  } catch (error) {
    console.error("Error taking photo:", error);
  }
};


